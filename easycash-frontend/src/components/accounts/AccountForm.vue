<template>
  <base-form
    ref="formRef"
    :loading="loading"
    :submit-label="submitLabel"
    @submit="onSubmit"
    @reset="onReset"
  >
    <q-input
      v-model="form.name"
      label="Account Name"
      :rules="[val => !!val || 'Account name is required']"
      lazy-rules
    />

    <q-select
      v-model="form.type"
      :options="accountTypes"
      label="Account Type"
      :rules="[val => !!val || 'Account type is required']"
      lazy-rules
    />

    <q-input
      v-model.number="form.balance"
      label="Initial Balance"
      type="number"
      step="0.01"
      :rules="[val => val !== null && val !== undefined || 'Balance is required']"
      lazy-rules
    >
      <template v-slot:prepend>
        <q-icon name="attach_money" />
      </template>
    </q-input>

    <q-select
      v-model="form.currency"
      :options="currencies"
      label="Currency"
      :rules="[val => !!val || 'Currency is required']"
      lazy-rules
    />

    <q-select
      v-model="form.icon"
      :options="icons"
      label="Icon"
      emit-value
      map-options
      option-label="label"
      option-value="value"
    >
      <template v-slot:option="scope">
        <q-item v-bind="scope.itemProps">
          <q-item-section avatar>
            <q-icon :name="scope.opt.value" />
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ scope.opt.label }}</q-item-label>
          </q-item-section>
        </q-item>
      </template>

      <template v-slot:selected>
        <q-icon :name="form.icon || 'account_balance'" class="q-mr-sm" />
        {{ getIconLabel(form.icon) }}
      </template>
    </q-select>

    <q-select
      v-model="form.color"
      :options="colors"
      label="Color"
      emit-value
      map-options
      option-label="label"
      option-value="value"
    >
      <template v-slot:option="scope">
        <q-item v-bind="scope.itemProps">
          <q-item-section avatar>
            <q-badge :style="`background-color: ${scope.opt.value}`" style="width: 25px; height: 25px" />
          </q-item-section>
          <q-item-section>
            <q-item-label>{{ scope.opt.label }}</q-item-label>
          </q-item-section>
        </q-item>
      </template>

      <template v-slot:selected>
        <q-badge :style="`background-color: ${form.color}`" style="width: 25px; height: 25px" class="q-mr-sm" />
        {{ getColorLabel(form.color) }}
      </template>
    </q-select>

    <q-toggle
      v-model="form.is_default"
      label="Set as default account"
    />
  </base-form>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue';
import BaseForm from 'src/components/common/BaseForm.vue';
import type { Account, AccountRequest } from 'src/services/account.service';

const props = defineProps({
  account: {
    type: Object as () => Account | null,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'create',
    validator: (value: string) => ['create', 'edit'].includes(value)
  }
});

const emit = defineEmits(['submit', 'reset']);

const formRef = ref<InstanceType<typeof BaseForm> | null>(null);

const form = ref<AccountRequest>({
  name: '',
  type: 'checking',
  balance: 0,
  currency: 'USD',
  icon: 'account_balance',
  color: '#1976D2',
  is_default: false
});

// Account types
const accountTypes = [
  'checking',
  'savings',
  'credit',
  'cash',
  'investment',
  'loan',
  'other'
];

// Currencies
const currencies = [
  'USD',
  'EUR',
  'GBP',
  'JPY',
  'CAD',
  'AUD',
  'CNY',
  'INR'
];

// Icons
const icons = [
  { label: 'Bank', value: 'account_balance' },
  { label: 'Card', value: 'credit_card' },
  { label: 'Cash', value: 'payments' },
  { label: 'Wallet', value: 'account_balance_wallet' },
  { label: 'Savings', value: 'savings' },
  { label: 'Investment', value: 'trending_up' },
  { label: 'Loan', value: 'money_off' },
  { label: 'Other', value: 'more_horiz' }
];

// Colors
const colors = [
  { label: 'Blue', value: '#1976D2' },
  { label: 'Red', value: '#C62828' },
  { label: 'Green', value: '#2E7D32' },
  { label: 'Purple', value: '#7B1FA2' },
  { label: 'Orange', value: '#EF6C00' },
  { label: 'Teal', value: '#00796B' },
  { label: 'Pink', value: '#C2185B' },
  { label: 'Grey', value: '#616161' }
];

// Get icon label
const getIconLabel = (value: string | undefined) => {
  if (!value) return 'Bank';
  const icon = icons.find(i => i.value === value);
  return icon ? icon.label : 'Bank';
};

// Get color label
const getColorLabel = (value: string | undefined) => {
  if (!value) return 'Blue';
  const color = colors.find(c => c.value === value);
  return color ? color.label : 'Blue';
};

// Submit label
const submitLabel = props.mode === 'create' ? 'Create Account' : 'Update Account';

// Initialize form with account data if in edit mode
watch(() => props.account, (newAccount) => {
  if (newAccount) {
    form.value = {
      name: newAccount.name,
      type: newAccount.type,
      balance: newAccount.balance,
      currency: newAccount.currency,
      icon: newAccount.icon || 'account_balance',
      color: newAccount.color || '#1976D2',
      is_default: newAccount.is_default
    };
  }
}, { immediate: true });

// Submit form
const onSubmit = () => {
  emit('submit', { ...form.value });
};

// Reset form
const onReset = () => {
  if (props.account) {
    form.value = {
      name: props.account.name,
      type: props.account.type,
      balance: props.account.balance,
      currency: props.account.currency,
      icon: props.account.icon || 'account_balance',
      color: props.account.color || '#1976D2',
      is_default: props.account.is_default
    };
  } else {
    form.value = {
      name: '',
      type: 'checking',
      balance: 0,
      currency: 'USD',
      icon: 'account_balance',
      color: '#1976D2',
      is_default: false
    };
  }

  emit('reset');
};

// Expose methods to parent component
defineExpose({
  validate: async () => await formRef.value?.validate(),
  reset: () => formRef.value?.reset()
});
</script>
