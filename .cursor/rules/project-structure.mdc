---
description: 
globs: 
alwaysApply: true
---
# EasyCash 项目结构规范

## 前端结构 (easycash-frontend)
前端应用采用 Vue 3 + Quasar v2 + TypeScript 技术栈：

```
easycash-frontend/
├── src/
│   ├── assets/           # 静态资源
│   │   ├── images/      # 图片资源
│   │   ├── icons/       # 图标资源
│   │   └── fonts/       # 字体资源
│   ├── boot/            # 应用初始化和插件
│   │   ├── axios.ts     # API 请求配置
│   │   ├── i18n.ts      # 国际化配置
│   │   └── pinia.ts     # 状态管理配置
│   ├── components/      # 可复用的 Vue 组件
│   │   ├── common/      # 通用组件
│   │   ├── form/        # 表单相关组件
│   │   ├── layout/      # 布局相关组件
│   │   └── transaction/ # 交易相关组件
│   ├── composables/     # Vue 组合式 API 钩子
│   │   ├── useAuth.ts
│   │   ├── useTransaction.ts
│   │   └── useNotification.ts
│   ├── css/            # 全局样式和主题
│   │   ├── app.scss
│   │   ├── quasar.variables.scss
│   │   └── themes/
│   ├── i18n/           # 国际化文件
│   │   ├── en-US/
│   │   └── zh-CN/
│   ├── layouts/        # 页面布局模板
│   │   ├── MainLayout.vue
│   │   └── AuthLayout.vue
│   ├── pages/          # 路由页面
│   │   ├── auth/
│   │   ├── dashboard/
│   │   ├── transaction/
│   │   └── settings/
│   ├── router/         # Vue 路由配置
│   │   ├── index.ts
│   │   └── routes.ts
│   ├── stores/         # Pinia 状态管理
│   │   ├── auth.store.ts
│   │   ├── user.store.ts
│   │   ├── transaction.store.ts
│   │   └── category.store.ts
│   └── types/          # TypeScript 类型定义
│       ├── models/
│       └── api/
├── public/            # 静态公共资源
├── test/             # 测试文件
└── src-capacitor/    # 移动应用配置
```

## 后端结构 (easycash-backend)
后端应用采用 Go + Gin 技术栈：

```
easycash-backend/
├── cmd/                # 应用入口
│   └── server/        # 服务器入口
├── configs/           # 配置文件
│   ├── config.go
│   └── config.yaml
├── internal/          # 内部包
│   ├── api/          # API 处理器
│   │   ├── auth/
│   │   ├── user/
│   │   ├── transaction/
│   │   └── category/
│   ├── middleware/    # 中间件
│   ├── model/        # 数据模型
│   ├── repository/   # 数据访问层
│   ├── service/      # 业务逻辑层
│   └── utils/        # 工具函数
├── pkg/              # 可导出的包
│   ├── logger/
│   ├── database/
│   └── auth/
├── migrations/       # 数据库迁移
├── scripts/         # 脚本文件
└── test/           # 测试文件
```

## 命名规范

1. 前端命名规范：

   组件 (Components):
   - 使用 PascalCase
   - 添加 .vue 扩展名
   - 示例: `TransactionList.vue`

   存储 (Stores):
   - 使用 camelCase
   - 添加 .store.ts 扩展名
   - 示例: `transaction.store.ts`

   页面 (Pages):
   - 使用 kebab-case
   - 放在 pages/ 目录下
   - 示例: `transaction-history.vue`

   组合式 API (Composables):
   - 使用 camelCase
   - 前缀为 'use'
   - 示例: `useTransaction.ts`

2. 后端命名规范：

   包名 (Packages):
   - 使用小写
   - 简短且有意义
   - 示例: `repository`, `service`

   接口 (Interfaces):
   - 使用大驼峰
   - 示例: `type UserRepository interface`

   结构体 (Structs):
   - 使用大驼峰
   - 示例: `type User struct`

   方法 (Methods):
   - 使用大驼峰
   - 示例: `func (s *Service) CreateUser()`

## 文件组织原则

1. 前端文件组织：
   - 按功能模块组织目录
   - 相关组件放在同一目录
   - 使用 index.ts 统一导出
   - 共享组件放在 common 目录

2. 后端文件组织：
   - 遵循 Go 项目标准布局
   - 按职责分层（API、Service、Repository）
   - 内部包放在 internal 目录
   - 可重用包放在 pkg 目录

3. 测试文件组织：
   前端：
   - 单元测试与源文件同目录
   - 使用 .spec.ts 后缀
   - E2E 测试放在 test/e2e/

   后端：
   - 单元测试与源文件同包
   - 使用 _test.go 后缀
   - 集成测试放在 test/ 目录

4. 资源文件组织：
   - 静态资源放在 assets/ 目录
   - 配置文件放在 configs/ 目录
   - 数据库迁移放在 migrations/ 目录 