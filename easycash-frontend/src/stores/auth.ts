import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { api } from 'src/boot/axios';

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<{id: number; username: string; email: string} | null>(null);
  const token = ref<string | null>(null);
  const isLoading = ref(false);

  // Initialize state from localStorage if available
  const storedUser = localStorage.getItem('user');
  const storedToken = localStorage.getItem('token');
  
  if (storedUser) {
    user.value = JSON.parse(storedUser);
  }
  
  if (storedToken) {
    token.value = storedToken;
  }

  // Getters
  const isAuthenticated = computed(() => !!token.value);
  
  // Actions
  async function login(email: string, password: string) {
    // eslint-disable-next-line no-console
    console.log('Logging in with:', email, password);
    isLoading.value = true;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      // const response = await api.post('/auth/login', { email, password });
      
      // For demo, just set some mock data
      const mockUser = {
        id: 1,
        username: email.split('@')[0],
        email: email
      };
      
      user.value = mockUser;
      token.value = 'mock-token'; // Set token instead of isAuthenticated
      
      // Store in localStorage
      localStorage.setItem('user', JSON.stringify(mockUser));
      localStorage.setItem('token', 'mock-token');
      
      // Set auth header
      setAuthHeader();
      
      return { success: true };
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('Login error:', err);
      return {
        success: false,
        error: 'Invalid credentials'
      };
    } finally {
      isLoading.value = false;
    }
  }
  
  async function register(username: string, email: string, password: string) {
    // eslint-disable-next-line no-console
    console.log('Registering user:', username, email, password);
    isLoading.value = true;
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you would make an API call here
      // const response = await api.post('/auth/register', { username, email, password });
      
      // For demo, just set some mock data
      const mockUser = {
        id: 1,
        username: username,
        email: email
      };
      
      user.value = mockUser;
      token.value = 'mock-token'; // Set token instead of isAuthenticated
      
      // Store in localStorage
      localStorage.setItem('user', JSON.stringify(mockUser));
      localStorage.setItem('token', 'mock-token');
      
      // Set auth header
      setAuthHeader();
      
      return { success: true };
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('Registration error:', err);
      return {
        success: false,
        error: 'Registration failed'
      };
    } finally {
      isLoading.value = false;
    }
  }
  
  function logout() {
    // Clear state
    user.value = null;
    token.value = null;
    
    // Clear localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('isAuthenticated');
    
    // Clear auth header
    api.defaults.headers.common['Authorization'] = '';
  }
  
  function setAuthHeader() {
    if (token.value) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token.value}`;
    }
  }
  
  // Initialize auth header on store creation
  setAuthHeader();
  
  return {
    user,
    token,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout
  };
}); 