<template>
  <q-page class="q-pa-md">
    <div class="row items-center q-mb-md">
      <div class="col">
        <div class="text-h5">Statistics</div>
        <div class="text-subtitle2 text-grey-7">Financial analytics and reports</div>
      </div>
      <div class="col-auto">
        <q-btn-dropdown color="primary" label="Reports" icon="file_download">
          <q-list>
            <q-item clickable v-close-popup @click="exportReport('pdf')">
              <q-item-section avatar>
                <q-icon name="picture_as_pdf" color="negative" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Export as PDF</q-item-label>
              </q-item-section>
            </q-item>
            <q-item clickable v-close-popup @click="exportReport('csv')">
              <q-item-section avatar>
                <q-icon name="table_view" color="green" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Export as CSV</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-btn-dropdown>
      </div>
    </div>

    <!-- Filter Controls -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-4">
            <q-select
              v-model="filters.period"
              :options="periodOptions"
              label="Time Period"
              outlined
              dense
              emit-value
              map-options
              @update:model-value="fetchData"
            />
          </div>
          <div class="col-12 col-md-4">
            <q-select
              v-model="filters.account"
              :options="accountOptions"
              label="Account"
              outlined
              dense
              emit-value
              map-options
              clearable
              @update:model-value="fetchData"
            />
          </div>
          <div class="col-12 col-md-4">
            <q-select
              v-model="filters.category"
              :options="categoryOptions"
              label="Category"
              outlined
              dense
              emit-value
              map-options
              clearable
              @update:model-value="fetchData"
            />
          </div>
        </div>
        
        <div class="row q-col-gutter-md q-mt-sm">
          <div class="col-12 col-md-6">
            <q-input
              v-model="dateRangeModel"
              outlined
              dense
              label="Custom Date Range"
              mask="date-range"
              :disable="filters.period !== 'custom'"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                    <q-date v-model="filters.dateRange" range />
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>
          <div class="col-12 col-md-6">
            <q-btn 
              color="primary" 
              icon="filter_list" 
              label="Apply Filters" 
              class="full-width" 
              @click="fetchData"
              :disable="isCustomDateRangeEmpty"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Stats Summary Cards -->
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card">
          <q-card-section>
            <div class="text-subtitle2 text-grey-7">Total Income</div>
            <div class="text-h5 text-positive">{{ formatCurrency(summary.income) }}</div>
            <div class="text-caption">
              <q-icon
                :name="summary.incomeChange >= 0 ? 'arrow_upward' : 'arrow_downward'"
                :color="summary.incomeChange >= 0 ? 'positive' : 'negative'"
                size="16px"
              />
              {{ Math.abs(summary.incomeChange) }}% from previous period
            </div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card">
          <q-card-section>
            <div class="text-subtitle2 text-grey-7">Total Expenses</div>
            <div class="text-h5 text-negative">{{ formatCurrency(summary.expenses) }}</div>
            <div class="text-caption">
              <q-icon
                :name="summary.expensesChange <= 0 ? 'arrow_downward' : 'arrow_upward'"
                :color="summary.expensesChange <= 0 ? 'positive' : 'negative'"
                size="16px"
              />
              {{ Math.abs(summary.expensesChange) }}% from previous period
            </div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card">
          <q-card-section>
            <div class="text-subtitle2 text-grey-7">Net Savings</div>
            <div class="text-h5" :class="summary.savings >= 0 ? 'text-positive' : 'text-negative'">
              {{ formatCurrency(summary.savings) }}
            </div>
            <div class="text-caption">
              <q-icon
                :name="summary.savingsChange >= 0 ? 'arrow_upward' : 'arrow_downward'"
                :color="summary.savingsChange >= 0 ? 'positive' : 'negative'"
                size="16px"
              />
              {{ Math.abs(summary.savingsChange) }}% from previous period
            </div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-12 col-sm-6 col-md-3">
        <q-card class="stats-card">
          <q-card-section>
            <div class="text-subtitle2 text-grey-7">Transactions</div>
            <div class="text-h5">{{ summary.transactions }}</div>
            <div class="text-caption">
              <q-icon
                :name="summary.transactionsChange >= 0 ? 'arrow_upward' : 'arrow_downward'"
                :color="summary.transactionsChange >= 0 ? 'blue' : 'orange'"
                size="16px"
              />
              {{ Math.abs(summary.transactionsChange) }}% from previous period
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="row q-col-gutter-md">
      <!-- Income vs Expenses Chart -->
      <div class="col-12 col-lg-8">
        <q-card>
          <q-card-section>
            <div class="text-h6">Income vs Expenses</div>
            <div class="text-subtitle2 text-grey-7">{{ getPeriodLabel() }}</div>
          </q-card-section>
          
          <q-card-section class="chart-container">
            <!-- Here we would normally use a charting library like Chart.js -->
            <!-- For this template, we'll use a placeholder -->
            <div v-if="loading" class="full-width row flex-center q-pa-xl">
              <q-spinner color="primary" size="3em" />
            </div>
            <div v-else class="income-expense-chart">
              <!-- This is a placeholder for the actual chart -->
              <div class="chart-placeholder">
                <div class="chart-bar" v-for="(item, index) in barChartData" :key="index">
                  <div class="chart-label">{{ item.label }}</div>
                  <div class="chart-columns">
                    <div 
                      class="income-column" 
                      :style="{ height: (item.income / maxValue * 100) + '%' }"
                    >
                      <div class="value-label">{{ formatCurrency(item.income) }}</div>
                    </div>
                    <div 
                      class="expense-column" 
                      :style="{ height: (item.expense / maxValue * 100) + '%' }"
                    >
                      <div class="value-label">{{ formatCurrency(item.expense) }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="chart-legend">
                <div class="legend-item">
                  <div class="legend-color income-color"></div>
                  <div>Income</div>
                </div>
                <div class="legend-item">
                  <div class="legend-color expense-color"></div>
                  <div>Expenses</div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
      
      <!-- Category Breakdown Pie Chart -->
      <div class="col-12 col-lg-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">Spending by Category</div>
            <div class="text-subtitle2 text-grey-7">{{ getPeriodLabel() }}</div>
          </q-card-section>
          
          <q-card-section class="chart-container">
            <!-- Placeholder for pie chart -->
            <div v-if="loading" class="full-width row flex-center q-pa-xl">
              <q-spinner color="primary" size="3em" />
            </div>
            <div v-else class="pie-chart-container">
              <div class="pie-chart-placeholder">
                <!-- Empty placeholder, would contain actual chart -->
              </div>
              <div class="pie-legend">
                <div 
                  v-for="(category, index) in categoryData" 
                  :key="index" 
                  class="pie-legend-item"
                >
                  <div class="legend-color" :style="{ backgroundColor: category.color }"></div>
                  <div class="legend-label">{{ category.name }}</div>
                  <div class="legend-value">{{ formatCurrency(category.amount) }}</div>
                  <div class="legend-percentage">({{ category.percentage }}%)</div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
      
      <!-- Monthly Trend Line Chart -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">Monthly Trends</div>
            <div class="text-subtitle2 text-grey-7">Balance over time</div>
          </q-card-section>
          
          <q-card-section class="chart-container">
            <!-- Placeholder for line chart -->
            <div v-if="loading" class="full-width row flex-center q-pa-xl">
              <q-spinner color="primary" size="3em" />
            </div>
            <div v-else class="line-chart-placeholder">
              <!-- Empty placeholder, would contain actual chart -->
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';

// Define proper interfaces for our data structures
interface ChartItem {
  label: string;
  income: number;
  expense: number;
}

interface CategoryItem {
  name: string;
  amount: number;
  percentage: number;
  color: string;
}

interface DateRange {
  from: string;
  to: string;
}

const $q = useQuasar();
const loading = ref(false);

// Filter state
const filters = reactive({
  period: 'month' as 'week' | 'month' | 'quarter' | 'year' | 'custom',
  account: null as string | null,
  category: null as string | null,
  dateRange: { from: '', to: '' } as DateRange
});

// 修改计算属性转换日期范围格式
const dateRangeModel = computed<string>({
  get: () => {
    // 返回空字符串，实际日期控制仍由q-date处理
    return '';
  },
  set: () => {
    // 空实现，我们会在q-date组件上直接绑定修改filters.dateRange
  }
});

// 处理条件表达式
const isCustomDateRangeEmpty = computed(() => {
  return filters.period === 'custom' && !filters.dateRange.from;
});

// Options for dropdowns
const periodOptions = [
  { label: 'Last 7 Days', value: 'week' },
  { label: 'This Month', value: 'month' },
  { label: 'Last 3 Months', value: 'quarter' },
  { label: 'This Year', value: 'year' },
  { label: 'Custom Range', value: 'custom' }
];

const accountOptions = [
  { label: 'All Accounts', value: null },
  { label: 'Main Account', value: 'Main Account' },
  { label: 'Savings', value: 'Savings' },
  { label: 'Credit Card', value: 'Credit Card' }
];

const categoryOptions = [
  { label: 'All Categories', value: null },
  { label: 'Food', value: 'Food' },
  { label: 'Transportation', value: 'Transportation' },
  { label: 'Housing', value: 'Housing' },
  { label: 'Entertainment', value: 'Entertainment' },
  { label: 'Shopping', value: 'Shopping' }
];

// Summary data
const summary = ref({
  income: 0,
  incomeChange: 0,
  expenses: 0,
  expensesChange: 0,
  savings: 0,
  savingsChange: 0,
  transactions: 0,
  transactionsChange: 0
});

// Chart data with proper type definitions
const barChartData = ref<ChartItem[]>([]);
const categoryData = ref<CategoryItem[]>([]);
const maxValue = computed(() => {
  if (!barChartData.value.length) return 1000;
  
  return Math.max(
    ...barChartData.value.map(item => Math.max(item.income, item.expense))
  ) * 1.1; // Add 10% buffer for visualization
});

// Helper functions
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(value);
};

const getPeriodLabel = () => {
  const today = new Date();
  const labels: Record<string, string> = {
    week: `Last 7 days (${new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toLocaleDateString()} - ${today.toLocaleDateString()})`,
    month: `${today.toLocaleString('default', { month: 'long' })} ${today.getFullYear()}`,
    quarter: `Last 3 months`,
    year: `${today.getFullYear()}`,
    custom: filters.dateRange.from && filters.dateRange.to ? 
      `${filters.dateRange.from} - ${filters.dateRange.to}` : 'Custom Range'
  };
  
  return labels[filters.period] || '';
};

const exportReport = (format: string) => {
  $q.notify({
    color: 'positive',
    message: `Exporting report as ${format.toUpperCase()}...`,
    icon: 'file_download'
  });
  
  // In a real app, this would trigger an API call to generate and download the report
  setTimeout(() => {
    $q.notify({
      color: 'positive',
      message: `Report exported successfully`,
      icon: 'check_circle'
    });
  }, 2000);
};

// Data fetching
const fetchData = async () => {
  loading.value = true;
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock summary data
    summary.value = {
      income: 4250.75,
      incomeChange: 12.5,
      expenses: 3120.45,
      expensesChange: 8.2,
      savings: 1130.30,
      savingsChange: 15.8,
      transactions: 42,
      transactionsChange: -3.5
    };
    
    // Mock bar chart data for income vs expenses
    if (filters.period === 'week') {
      barChartData.value = [
        { label: 'Mon', income: 150, expense: 85.50 },
        { label: 'Tue', income: 0, expense: 120.75 },
        { label: 'Wed', income: 0, expense: 67.20 },
        { label: 'Thu', income: 0, expense: 45.30 },
        { label: 'Fri', income: 2100, expense: 230.80 },
        { label: 'Sat', income: 0, expense: 180.45 },
        { label: 'Sun', income: 0, expense: 90.25 }
      ];
    } else if (filters.period === 'month') {
      barChartData.value = [
        { label: 'Week 1', income: 2100, expense: 850.50 },
        { label: 'Week 2', income: 0, expense: 720.75 },
        { label: 'Week 3', income: 2150, expense: 870.20 },
        { label: 'Week 4', income: 0, expense: 645.30 }
      ];
    } else if (filters.period === 'quarter') {
      barChartData.value = [
        { label: 'Jul', income: 4100, expense: 3150.50 },
        { label: 'Aug', income: 4250, expense: 3220.75 },
        { label: 'Sep', income: 4300, expense: 3180.20 }
      ];
    } else {
      barChartData.value = [
        { label: 'Jan', income: 4000, expense: 3100 },
        { label: 'Feb', income: 4100, expense: 3150 },
        { label: 'Mar', income: 4050, expense: 3200 },
        { label: 'Apr', income: 4200, expense: 3250 },
        { label: 'May', income: 4150, expense: 3100 },
        { label: 'Jun', income: 4250, expense: 3150 },
        { label: 'Jul', income: 4100, expense: 3050 },
        { label: 'Aug', income: 4250, expense: 3200 },
        { label: 'Sep', income: 4300, expense: 3150 },
        { label: 'Oct', income: 0, expense: 0 },
        { label: 'Nov', income: 0, expense: 0 },
        { label: 'Dec', income: 0, expense: 0 }
      ];
    }
    
    // Mock category data for pie chart
    categoryData.value = [
      { name: 'Food', amount: 850.45, percentage: 27, color: '#FF5252' },
      { name: 'Housing', amount: 1200, percentage: 38, color: '#2196F3' },
      { name: 'Transportation', amount: 450.30, percentage: 15, color: '#4CAF50' },
      { name: 'Entertainment', amount: 320.75, percentage: 10, color: '#9C27B0' },
      { name: 'Shopping', amount: 298.95, percentage: 10, color: '#FF9800' }
    ];
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Failed to load statistics data:', err);
    $q.notify({
      color: 'negative',
      message: 'Failed to load statistics data',
      icon: 'error'
    });
  } finally {
    loading.value = false;
  }
};

// Initialize
onMounted(() => {
  void fetchData();
});
</script>

<style lang="scss">
.stats-card {
  transition: transform 0.3s;
  
  &:hover {
    transform: translateY(-5px);
  }
}

.chart-container {
  min-height: 300px;
}

// Placeholder styling for income vs expenses chart
.income-expense-chart {
  height: 300px;
  position: relative;
  
  .chart-placeholder {
    display: flex;
    height: 250px;
    align-items: flex-end;
    justify-content: space-around;
    
    .chart-bar {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      
      .chart-label {
        margin-top: 10px;
        font-size: 12px;
      }
      
      .chart-columns {
        display: flex;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: flex-end;
        gap: 5px;
        
        .income-column, .expense-column {
          width: 30px;
          position: relative;
          transition: height 0.5s;
          
          .value-label {
            position: absolute;
            top: -20px;
            font-size: 10px;
            width: 100%;
            text-align: center;
          }
        }
        
        .income-column {
          background-color: #4CAF50;
        }
        
        .expense-column {
          background-color: #F44336;
        }
      }
    }
  }
  
  .chart-legend {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    
    .legend-item {
      display: flex;
      align-items: center;
      margin: 0 10px;
      
      .legend-color {
        width: 15px;
        height: 15px;
        margin-right: 5px;
      }
      
      .income-color {
        background-color: #4CAF50;
      }
      
      .expense-color {
        background-color: #F44336;
      }
    }
  }
}

// Placeholder styling for pie chart
.pie-chart-container {
  display: flex;
  flex-direction: column;
  
  .pie-chart-placeholder {
    height: 150px;
    background: conic-gradient(
      #FF5252 0% 27%,
      #2196F3 27% 65%,
      #4CAF50 65% 80%,
      #9C27B0 80% 90%,
      #FF9800 90% 100%
    );
    border-radius: 50%;
    width: 150px;
    margin: 0 auto;
  }
  
  .pie-legend {
    margin-top: 20px;
    
    .pie-legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .legend-color {
        width: 15px;
        height: 15px;
        margin-right: 8px;
      }
      
      .legend-label {
        flex: 1;
      }
      
      .legend-value {
        margin-right: 8px;
      }
    }
  }
}

// Placeholder styling for line chart
.line-chart-placeholder {
  height: 300px;
  background: linear-gradient(180deg, rgba(33, 150, 243, 0.1) 0%, rgba(33, 150, 243, 0) 100%);
  position: relative;
  
  &:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: #2196F3;
    animation: wave 4s infinite ease-in-out;
  }
}

@keyframes wave {
  0% { transform: translateY(0) scaleY(1); }
  50% { transform: translateY(30px) scaleY(0.5); }
  100% { transform: translateY(0) scaleY(1); }
}
</style> 