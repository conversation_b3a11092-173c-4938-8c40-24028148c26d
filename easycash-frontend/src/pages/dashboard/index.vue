<template>
  <q-page padding>
    <div class="row q-col-gutter-md">
      <!-- 账户概览 -->
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">账户概览</div>
            <div class="text-h4 q-mt-sm">¥ 25,380.00</div>
            <div class="text-caption text-grey">总资产</div>
          </q-card-section>
          <q-separator />
          <q-card-section>
            <div class="row items-center justify-between q-mb-sm">
              <div class="text-subtitle2">现金</div>
              <div class="text-subtitle1">¥ 5,380.00</div>
            </div>
            <div class="row items-center justify-between">
              <div class="text-subtitle2">银行卡</div>
              <div class="text-subtitle1">¥ 20,000.00</div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- 本月预算 -->
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">本月预算</div>
            <div class="text-h4 q-mt-sm">¥ 8,000.00</div>
            <div class="text-caption text-grey">剩余预算</div>
          </q-card-section>
          <q-separator />
          <q-card-section>
            <q-linear-progress
              rounded
              size="md"
              :value="0.6"
              color="primary"
              class="q-mb-sm"
            />
            <div class="row items-center justify-between">
              <div class="text-caption text-grey">已使用: ¥ 12,000.00</div>
              <div class="text-caption text-grey">总预算: ¥ 20,000.00</div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- 快速操作 -->
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">快速操作</div>
          </q-card-section>
          <q-card-section class="q-pa-none">
            <q-list>
              <q-item clickable v-ripple to="/transactions/new">
                <q-item-section avatar>
                  <q-icon name="add_circle" color="primary" />
                </q-item-section>
                <q-item-section>记一笔</q-item-section>
              </q-item>
              <q-item clickable v-ripple to="/budgets">
                <q-item-section avatar>
                  <q-icon name="savings" color="primary" />
                </q-item-section>
                <q-item-section>设置预算</q-item-section>
              </q-item>
              <q-item clickable v-ripple to="/statistics">
                <q-item-section avatar>
                  <q-icon name="analytics" color="primary" />
                </q-item-section>
                <q-item-section>查看统计</q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </q-card>
      </div>

      <!-- 最近交易 -->
      <div class="col-12 col-md-8">
        <q-card>
          <q-card-section class="row items-center">
            <div class="text-h6">最近交易</div>
            <q-space />
            <q-btn flat color="primary" label="查看全部" to="/transactions" />
          </q-card-section>
          <q-separator />
          <q-list>
            <q-item v-for="transaction in recentTransactions" :key="transaction.id">
              <q-item-section avatar>
                <q-icon :name="transaction.icon" :color="transaction.type === 'expense' ? 'negative' : 'positive'" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ transaction.description }}</q-item-label>
                <q-item-label caption>{{ transaction.category }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-item-label :class="{ 'text-negative': transaction.type === 'expense', 'text-positive': transaction.type === 'income' }">
                  {{ transaction.type === 'expense' ? '-' : '+' }}¥ {{ transaction.amount }}
                </q-item-label>
                <q-item-label caption>{{ transaction.date }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card>
      </div>

      <!-- 支出分析 -->
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">支出分析</div>
          </q-card-section>
          <q-card-section>
            <div class="text-center" style="height: 200px">
              <!-- 这里将添加饼图组件 -->
              <div class="text-grey">支出分类占比图表</div>
            </div>
          </q-card-section>
          <q-separator />
          <q-list>
            <q-item v-for="category in expenseCategories" :key="category.name">
              <q-item-section>
                <q-item-label>{{ category.name }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-item-label>¥ {{ category.amount }}</q-item-label>
                <q-item-label caption>{{ category.percentage }}%</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
defineOptions({
  name: 'DashboardPage'
})

// 模拟数据
const recentTransactions = [
  {
    id: 1,
    type: 'expense',
    description: '午餐',
    category: '餐饮',
    amount: '25.00',
    date: '2024-03-10',
    icon: 'restaurant'
  },
  {
    id: 2,
    type: 'expense',
    description: '打车',
    category: '交通',
    amount: '35.00',
    date: '2024-03-10',
    icon: 'local_taxi'
  },
  {
    id: 3,
    type: 'income',
    description: '工资',
    category: '工资收入',
    amount: '10000.00',
    date: '2024-03-09',
    icon: 'payments'
  }
]

const expenseCategories = [
  { name: '餐饮', amount: '3000.00', percentage: 30 },
  { name: '交通', amount: '2000.00', percentage: 20 },
  { name: '购物', amount: '1500.00', percentage: 15 },
  { name: '娱乐', amount: '1000.00', percentage: 10 }
]
</script>

<style lang="scss" scoped>
.q-card {
  height: 100%;
}
</style> 