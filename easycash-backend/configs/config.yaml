app:
  name: easycash
  mode: debug
  port: 8080
  jwt:
    secret: your-secret-key
    expire: 24h # 24 hours
  cors:
    allowed_origins:
      - "*"
    allowed_methods:
      - GET
      - POST
      - PUT
      - PATCH
      - DELETE
      - OPTIONS
    allowed_headers:
      - Origin
      - Content-Type
      - Accept
      - Authorization
    exposed_headers:
      - Content-Length
    max_age: 43200 # 12 hours

database:
  driver: postgres
  host: 127.0.0.1
  port: 5432
  user: postgres
  password: ops123456
  dbname: easycash
  sslmode: disable
  timezone: Asia/Shanghai

redis:
  host: localhost
  port: 6379
  password: "" 
  db: 0

log:
  level: debug
  filename: ./logs/easycash.log
  maxsize: 100 # megabytes
  maxage: 7 # days
  maxbackups: 10
  compress: true