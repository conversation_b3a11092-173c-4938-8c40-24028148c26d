package service

import (
	"errors"

	"gorm.io/gorm"

	"github.com/imaojia/easycash/internal/model"
)

// CategoryService handles category related operations
type CategoryService struct {
	db *gorm.DB
}

// NewCategoryService creates a new category service
func NewCategoryService(db *gorm.DB) *CategoryService {
	return &CategoryService{db: db}
}

// CreateCategory creates a new category for a user
func (s *CategoryService) CreateCategory(userID uint, req model.CategoryRequest) (*model.CategoryResponse, error) {
	// If parent ID is provided, check if it exists and belongs to the user
	if req.ParentID != nil {
		var parent model.Category
		if err := s.db.Where("id = ? AND user_id = ?", *req.ParentID, userID).First(&parent).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("parent category not found")
			}
			return nil, err
		}

		// Ensure parent and child have the same type
		if parent.Type != req.Type {
			return nil, errors.New("parent and child categories must have the same type")
		}
	}

	// Create new category
	category := model.Category{
		UserID:   userID,
		Name:     req.Name,
		Type:     req.Type,
		Icon:     req.Icon,
		Color:    req.Color,
		ParentID: req.ParentID,
	}

	// Save the category
	if err := s.db.Create(&category).Error; err != nil {
		return nil, err
	}

	return &model.CategoryResponse{
		ID:        category.ID,
		UserID:    category.UserID,
		Name:      category.Name,
		Type:      category.Type,
		Icon:      category.Icon,
		Color:     category.Color,
		ParentID:  category.ParentID,
		CreatedAt: category.CreatedAt,
		UpdatedAt: category.UpdatedAt,
	}, nil
}

// GetCategoryByID retrieves a category by ID
func (s *CategoryService) GetCategoryByID(userID, categoryID uint) (*model.Category, error) {
	var category model.Category
	if err := s.db.Where("id = ? AND user_id = ?", categoryID, userID).First(&category).Error; err != nil {
		return nil, err
	}
	return &category, nil
}

// GetCategoryWithChildren retrieves a category with its children
func (s *CategoryService) GetCategoryWithChildren(userID, categoryID uint) (*model.Category, error) {
	var category model.Category
	if err := s.db.Where("id = ? AND user_id = ?", categoryID, userID).First(&category).Error; err != nil {
		return nil, err
	}

	// Get children
	if err := s.db.Where("parent_id = ? AND user_id = ?", categoryID, userID).Find(&category.Children).Error; err != nil {
		return nil, err
	}

	return &category, nil
}

// UpdateCategory updates a category
func (s *CategoryService) UpdateCategory(userID, categoryID uint, req model.CategoryRequest) (*model.CategoryResponse, error) {
	// Get the category
	category, err := s.GetCategoryByID(userID, categoryID)
	if err != nil {
		return nil, err
	}

	// If parent ID is provided and changed, check if it exists and belongs to the user
	if req.ParentID != nil && (category.ParentID == nil || *category.ParentID != *req.ParentID) {
		// Check for circular reference
		if *req.ParentID == categoryID {
			return nil, errors.New("category cannot be its own parent")
		}

		var parent model.Category
		if err := s.db.Where("id = ? AND user_id = ?", *req.ParentID, userID).First(&parent).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("parent category not found")
			}
			return nil, err
		}

		// Ensure parent and child have the same type
		if parent.Type != req.Type {
			return nil, errors.New("parent and child categories must have the same type")
		}

		// Check if the new parent is a descendant of this category (would create a cycle)
		if err := s.checkForCyclicReference(categoryID, *req.ParentID); err != nil {
			return nil, err
		}
	}

	// Update fields
	category.Name = req.Name
	category.Type = req.Type
	category.Icon = req.Icon
	category.Color = req.Color
	category.ParentID = req.ParentID

	// Save the category
	if err := s.db.Save(category).Error; err != nil {
		return nil, err
	}

	return &model.CategoryResponse{
		ID:        category.ID,
		UserID:    category.UserID,
		Name:      category.Name,
		Type:      category.Type,
		Icon:      category.Icon,
		Color:     category.Color,
		ParentID:  category.ParentID,
		CreatedAt: category.CreatedAt,
		UpdatedAt: category.UpdatedAt,
	}, nil
}

// DeleteCategory deletes a category
func (s *CategoryService) DeleteCategory(userID, categoryID uint) error {
	// Get the category
	category, err := s.GetCategoryByID(userID, categoryID)
	if err != nil {
		return err
	}

	// Check if category has children
	var childCount int64
	if err := s.db.Model(&model.Category{}).Where("parent_id = ?", categoryID).Count(&childCount).Error; err != nil {
		return err
	}

	if childCount > 0 {
		return errors.New("cannot delete category with children")
	}

	// Check if category is used in transactions
	var transactionCount int64
	if err := s.db.Model(&model.Transaction{}).Where("category_id = ?", categoryID).Count(&transactionCount).Error; err != nil {
		return err
	}

	if transactionCount > 0 {
		return errors.New("cannot delete category used in transactions")
	}

	// Check if category is used in budgets
	var budgetCount int64
	if err := s.db.Model(&model.Budget{}).Where("category_id = ?", categoryID).Count(&budgetCount).Error; err != nil {
		return err
	}

	if budgetCount > 0 {
		return errors.New("cannot delete category used in budgets")
	}

	// Delete the category
	return s.db.Delete(category).Error
}

// ListCategories retrieves categories for a user
func (s *CategoryService) ListCategories(params model.CategoryListParams) ([]model.Category, int64, error) {
	var categories []model.Category
	var total int64

	// Build query
	query := s.db.Model(&model.Category{}).Where("user_id = ?", params.UserID)

	// Apply filters
	if params.Type != "" {
		query = query.Where("type = ?", params.Type)
	}

	if params.ParentID != nil {
		query = query.Where("parent_id = ?", *params.ParentID)
	} else {
		// If parent ID is not provided, get top-level categories
		query = query.Where("parent_id IS NULL")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (params.Page - 1) * params.Limit
	if err := query.Offset(offset).Limit(params.Limit).Find(&categories).Error; err != nil {
		return nil, 0, err
	}

	return categories, total, nil
}

// GetCategoryHierarchy retrieves the full category hierarchy for a user
func (s *CategoryService) GetCategoryHierarchy(userID uint, categoryType string) ([]model.Category, error) {
	var rootCategories []model.Category

	// Get root categories
	query := s.db.Where("user_id = ? AND parent_id IS NULL", userID)
	if categoryType != "" {
		query = query.Where("type = ?", categoryType)
	}

	if err := query.Find(&rootCategories).Error; err != nil {
		return nil, err
	}

	// For each root category, get its children
	for i := range rootCategories {
		if err := s.loadCategoryChildren(&rootCategories[i], userID); err != nil {
			return nil, err
		}
	}

	return rootCategories, nil
}

// loadCategoryChildren recursively loads children for a category
func (s *CategoryService) loadCategoryChildren(category *model.Category, userID uint) error {
	if err := s.db.Where("parent_id = ? AND user_id = ?", category.ID, userID).Find(&category.Children).Error; err != nil {
		return err
	}

	for i := range category.Children {
		if err := s.loadCategoryChildren(&category.Children[i], userID); err != nil {
			return err
		}
	}

	return nil
}

// checkForCyclicReference checks if adding parentID as a parent of categoryID would create a cycle
func (s *CategoryService) checkForCyclicReference(categoryID, parentID uint) error {
	// If the parent is the category itself, it's a cycle
	if categoryID == parentID {
		return errors.New("cyclic category reference detected")
	}

	// Check if any ancestor of the parent is the category
	var parent model.Category
	if err := s.db.First(&parent, parentID).Error; err != nil {
		return err
	}

	// If the parent has a parent, check that branch
	if parent.ParentID != nil {
		return s.checkForCyclicReference(categoryID, *parent.ParentID)
	}

	// No cycle detected
	return nil
}
