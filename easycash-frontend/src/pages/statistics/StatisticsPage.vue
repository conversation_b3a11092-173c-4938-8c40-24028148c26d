<template>
  <q-page padding>
    <page-header title="Statistics" />

    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading" message="Loading statistics..." />

    <!-- Date Range Selector -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-4">
            <q-select
              v-model="dateRange"
              :options="dateRangeOptions"
              label="Date Range"
              dense
              outlined
              @update:model-value="updateDateRange"
            />
          </div>

          <div class="col-12 col-md-4">
            <q-input
              v-model="startDate"
              label="Start Date"
              dense
              outlined
              :disable="dateRange !== 'custom'"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                    <q-date v-model="startDate" mask="YYYY-MM-DD" />
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>

          <div class="col-12 col-md-4">
            <q-input
              v-model="endDate"
              label="End Date"
              dense
              outlined
              :disable="dateRange !== 'custom'"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                    <q-date v-model="endDate" mask="YYYY-MM-DD" />
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>
        </div>

        <div class="row q-mt-md">
          <div class="col-12">
            <q-btn
              color="primary"
              label="Apply"
              @click="fetchStatistics"
              :loading="loading"
              class="q-mr-sm"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Summary Cards -->
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-12 col-md-4">
        <q-card class="bg-primary text-white">
          <q-card-section>
            <div class="text-subtitle1">Total Income</div>
            <div class="text-h4">{{ formatCurrency(totalIncome) }}</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-4">
        <q-card class="bg-negative text-white">
          <q-card-section>
            <div class="text-subtitle1">Total Expenses</div>
            <div class="text-h4">{{ formatCurrency(totalExpense) }}</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-4">
        <q-card :class="balanceClass">
          <q-card-section>
            <div class="text-subtitle1">Net Balance</div>
            <div class="text-h4">{{ formatCurrency(netBalance) }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Income vs Expense Chart -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6">Income vs Expenses</div>
        <div class="chart-container" style="height: 300px;">
          <canvas ref="incomeExpenseChart"></canvas>
        </div>
      </q-card-section>
    </q-card>

    <!-- Category Breakdown -->
    <div class="row q-col-gutter-md">
      <!-- Income Categories -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6">Income by Category</div>
            <div v-if="incomeCategoryStats.length === 0" class="text-center q-pa-md">
              <q-icon name="pie_chart" size="3rem" color="grey-5" />
              <div class="text-subtitle1 q-mt-sm">No income data available</div>
            </div>
            <div v-else class="chart-container" style="height: 300px;">
              <canvas ref="incomeCategoryChart"></canvas>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- Expense Categories -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6">Expenses by Category</div>
            <div v-if="expenseCategoryStats.length === 0" class="text-center q-pa-md">
              <q-icon name="pie_chart" size="3rem" color="grey-5" />
              <div class="text-subtitle1 q-mt-sm">No expense data available</div>
            </div>
            <div v-else class="chart-container" style="height: 300px;">
              <canvas ref="expenseCategoryChart"></canvas>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { date } from 'quasar';
import Chart from 'chart.js/auto';
import PageHeader from '../../components/common/PageHeader.vue';
import ErrorDisplay from '../../components/common/ErrorDisplay.vue';
import LoadingOverlay from '../../components/common/LoadingOverlay.vue';
import { useStatisticsStore } from '../../stores/statistics.store';
import { CategoryStatistics } from '../../services/statistics.service';
import NotificationService from '../../services/notification.service';

const statisticsStore = useStatisticsStore();

const loading = ref(false);
const error = ref<string | null>(null);

// Chart references
const incomeExpenseChart = ref<HTMLCanvasElement | null>(null);
const incomeCategoryChart = ref<HTMLCanvasElement | null>(null);
const expenseCategoryChart = ref<HTMLCanvasElement | null>(null);

// Chart instances
let incomeExpenseChartInstance: Chart | null = null;
let incomeCategoryChartInstance: Chart | null = null;
let expenseCategoryChartInstance: Chart | null = null;

// Date range
const dateRange = ref('month');
const startDate = ref(date.formatDate(date.subtractFromDate(new Date(), { months: 1 }), 'YYYY-MM-DD'));
const endDate = ref(date.formatDate(new Date(), 'YYYY-MM-DD'));

// Date range options
const dateRangeOptions = [
  { label: 'This Month', value: 'month' },
  { label: 'Last 3 Months', value: 'quarter' },
  { label: 'This Year', value: 'year' },
  { label: 'Custom Range', value: 'custom' }
];

// Computed properties
const dailyStats = computed(() => statisticsStore.dailyStats);
const categoryStats = computed(() => statisticsStore.categoryStats);

const totalIncome = computed(() => statisticsStore.totalIncome);
const totalExpense = computed(() => statisticsStore.totalExpense);
const netBalance = computed(() => totalIncome.value - totalExpense.value);

const balanceClass = computed(() => {
  return netBalance.value >= 0 ? 'bg-positive text-white' : 'bg-negative text-white';
});

const incomeCategoryStats = computed(() => {
  // TODO: Filter by income categories when type property is available
  return categoryStats.value;
});

const expenseCategoryStats = computed(() => {
  // TODO: Filter by expense categories when type property is available
  return categoryStats.value;
});

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Update date range based on selection
const updateDateRange = () => {
  const now = new Date();

  switch (dateRange.value) {
    case 'month':
      startDate.value = date.formatDate(new Date(now.getFullYear(), now.getMonth(), 1), 'YYYY-MM-DD');
      endDate.value = date.formatDate(now, 'YYYY-MM-DD');
      break;
    case 'quarter':
      startDate.value = date.formatDate(date.subtractFromDate(now, { months: 3 }), 'YYYY-MM-DD');
      endDate.value = date.formatDate(now, 'YYYY-MM-DD');
      break;
    case 'year':
      startDate.value = date.formatDate(new Date(now.getFullYear(), 0, 1), 'YYYY-MM-DD');
      endDate.value = date.formatDate(now, 'YYYY-MM-DD');
      break;
    // For custom, keep the current values
  }

  fetchStatistics();
};

// Fetch statistics
const fetchStatistics = async () => {
  loading.value = true;
  error.value = null;

  try {
    await Promise.all([
      statisticsStore.fetchDailyStatistics(startDate.value, endDate.value),
      statisticsStore.fetchCategoryStatistics(startDate.value, endDate.value)
    ]);

    renderCharts();
  } catch (err: any) {
    error.value = 'Failed to load statistics. Please try again.';
    NotificationService.error('Failed to load statistics');
    console.error('Statistics error:', err);
  } finally {
    loading.value = false;
  }
};

// Render charts
const renderCharts = () => {
  renderIncomeExpenseChart();
  renderCategoryCharts();
};

// Render income vs expense chart
const renderIncomeExpenseChart = () => {
  if (!incomeExpenseChart.value) return;

  // Destroy previous chart if it exists
  if (incomeExpenseChartInstance) {
    incomeExpenseChartInstance.destroy();
  }

  // Prepare data
  const labels = dailyStats.value.map(stat => date.formatDate(stat.date, 'MMM D'));
  const incomeData = dailyStats.value.map(stat => stat.income);
  const expenseData = dailyStats.value.map(stat => stat.expense);

  // Create chart
  incomeExpenseChartInstance = new Chart(incomeExpenseChart.value, {
    type: 'line',
    data: {
      labels,
      datasets: [
        {
          label: 'Income',
          data: incomeData,
          borderColor: '#21BA45',
          backgroundColor: 'rgba(33, 186, 69, 0.1)',
          fill: true
        },
        {
          label: 'Expenses',
          data: expenseData,
          borderColor: '#C10015',
          backgroundColor: 'rgba(193, 0, 21, 0.1)',
          fill: true
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });
};

// Render category charts
const renderCategoryCharts = () => {
  renderIncomeCategoryChart();
  renderExpenseCategoryChart();
};

// Render income category chart
const renderIncomeCategoryChart = () => {
  if (!incomeCategoryChart.value || incomeCategoryStats.value.length === 0) return;

  // Destroy previous chart if it exists
  if (incomeCategoryChartInstance) {
    incomeCategoryChartInstance.destroy();
  }

  // Prepare data
  const labels = incomeCategoryStats.value.map(stat => stat.category_name || 'Uncategorized');
  const data = incomeCategoryStats.value.map(stat => stat.amount);

  // Generate colors
  const backgroundColors = generateColors(incomeCategoryStats.value.length);

  // Create chart
  incomeCategoryChartInstance = new Chart(incomeCategoryChart.value, {
    type: 'pie',
    data: {
      labels,
      datasets: [
        {
          data,
          backgroundColor: backgroundColors
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        tooltip: {
          callbacks: {
            label: (context) => {
              const value = context.raw as number;
              return `${context.label}: ${formatCurrency(value)}`;
            }
          }
        }
      }
    }
  });
};

// Render expense category chart
const renderExpenseCategoryChart = () => {
  if (!expenseCategoryChart.value || expenseCategoryStats.value.length === 0) return;

  // Destroy previous chart if it exists
  if (expenseCategoryChartInstance) {
    expenseCategoryChartInstance.destroy();
  }

  // Prepare data
  const labels = expenseCategoryStats.value.map(stat => stat.category_name || 'Uncategorized');
  const data = expenseCategoryStats.value.map(stat => stat.amount);

  // Generate colors
  const backgroundColors = generateColors(expenseCategoryStats.value.length);

  // Create chart
  expenseCategoryChartInstance = new Chart(expenseCategoryChart.value, {
    type: 'pie',
    data: {
      labels,
      datasets: [
        {
          data,
          backgroundColor: backgroundColors
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        tooltip: {
          callbacks: {
            label: (context) => {
              const value = context.raw as number;
              return `${context.label}: ${formatCurrency(value)}`;
            }
          }
        }
      }
    }
  });
};

// Generate colors for charts
const generateColors = (count: number) => {
  const colors = [
    '#1976D2', // Blue
    '#C62828', // Red
    '#2E7D32', // Green
    '#7B1FA2', // Purple
    '#EF6C00', // Orange
    '#00796B', // Teal
    '#C2185B', // Pink
    '#616161'  // Grey
  ];

  // If we need more colors than in our palette, repeat them
  const result = [];
  for (let i = 0; i < count; i++) {
    result.push(colors[i % colors.length]);
  }

  return result;
};

// Watch for date changes in custom mode
watch([startDate, endDate], () => {
  if (dateRange.value === 'custom') {
    fetchStatistics();
  }
});

// Load data on component mount
onMounted(() => {
  updateDateRange();
});
</script>
