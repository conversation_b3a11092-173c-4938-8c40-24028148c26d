<template>
  <q-page padding>
    <page-header
      :title="mode === 'create' ? 'Create Category' : 'Edit Category'"
      show-back-button
    />

    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading && !formLoading" message="Loading category data..." />

    <q-card>
      <q-card-section>
        <category-form
          ref="formRef"
          :category="category"
          :loading="formLoading"
          :mode="mode"
          @submit="onSubmit"
          @reset="onReset"
        />
      </q-card-section>
    </q-card>

    <q-card v-if="isEditMode && category && transactions.length > 0" class="q-mt-md">
      <q-card-section class="q-pb-none">
        <div class="text-h6">Recent Transactions</div>
      </q-card-section>

      <q-card-section>
        <q-list separator>
          <q-item v-for="transaction in transactions.slice(0, 5)" :key="transaction.id" clickable @click="viewTransaction(transaction.id)">
            <q-item-section avatar>
              <q-avatar :color="getTransactionColor(transaction.type)" text-color="white">
                <q-icon :name="getTransactionIcon(transaction.type)" />
              </q-avatar>
            </q-item-section>

            <q-item-section>
              <q-item-label>{{ transaction.description || 'No description' }}</q-item-label>
              <q-item-label caption>{{ formatDate(transaction.date) }}</q-item-label>
            </q-item-section>

            <q-item-section side>
              <q-item-label :class="getAmountClass(transaction.type)">
                {{ getAmountPrefix(transaction.type) }}{{ formatCurrency(transaction.amount) }}
              </q-item-label>
              <q-item-label caption>{{ transaction.account || 'Unknown account' }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat color="primary" label="View All" :to="`/transactions?category_id=${categoryId}`" />
      </q-card-actions>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { date } from 'quasar';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import CategoryForm from 'src/components/categories/CategoryForm.vue';
import { useCategoryStore } from 'src/stores/category.store';
import { useTransactionStore } from 'src/stores/transaction.store';
import { CategoryRequest } from 'src/services/category.service';
import type { Transaction } from '../../types/models/transaction';
import NotificationService from 'src/services/notification.service';

const props = defineProps({
  id: {
    type: Number,
    default: 0
  },
  mode: {
    type: String,
    default: 'create',
    validator: (value: string) => ['create', 'edit'].includes(value)
  }
});

const router = useRouter();
const route = useRoute();
const categoryStore = useCategoryStore();
const transactionStore = useTransactionStore();

const formRef = ref<InstanceType<typeof CategoryForm> | null>(null);
const loading = ref(false);
const formLoading = ref(false);
const error = ref<string | null>(null);
const category = ref(categoryStore.currentCategory);
const transactions = ref<Transaction[]>([]);

// Computed properties
const isEditMode = computed(() => props.mode === 'edit');
const categoryId = computed(() => props.id || Number(route.params.id));

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Format date
const formatDate = (dateString: string) => {
  return date.formatDate(dateString, 'MMM D, YYYY');
};

// Get transaction icon
const getTransactionIcon = (type: string) => {
  switch (type) {
    case 'income':
      return 'arrow_upward';
    case 'expense':
      return 'arrow_downward';
    case 'transfer':
      return 'swap_horiz';
    default:
      return 'receipt_long';
  }
};

// Get transaction color
const getTransactionColor = (type: string) => {
  switch (type) {
    case 'income':
      return 'positive';
    case 'expense':
      return 'negative';
    case 'transfer':
      return 'info';
    default:
      return 'grey';
  }
};

// Get amount class
const getAmountClass = (type: string) => {
  switch (type) {
    case 'income':
      return 'text-positive';
    case 'expense':
      return 'text-negative';
    default:
      return '';
  }
};

// Get amount prefix
const getAmountPrefix = (type: string) => {
  switch (type) {
    case 'income':
      return '+';
    case 'expense':
      return '-';
    default:
      return '';
  }
};

// View transaction
const viewTransaction = (id: number) => {
  router.push(`/transactions/${id}`);
};

// Load category data if in edit mode
const loadCategory = async () => {
  if (!isEditMode.value || !categoryId.value) return;

  loading.value = true;
  error.value = null;

  try {
    await categoryStore.fetchCategory(categoryId.value);
    category.value = categoryStore.currentCategory;

    // Load transactions for this category
    await transactionStore.list({
      category_id: categoryId.value,
      limit: 5,
      sort_by: 'date',
      sort_order: 'desc'
    });

    transactions.value = transactionStore.transactions;
  } catch (err: any) {
    error.value = 'Failed to load category. Please try again.';
    NotificationService.error('Failed to load category');
    console.error('Load category error:', err);
  } finally {
    loading.value = false;
  }
};

// Submit form
const onSubmit = async (formData: CategoryRequest) => {
  formLoading.value = true;
  error.value = null;

  try {
    if (isEditMode.value && categoryId.value) {
      await categoryStore.updateCategory(categoryId.value, formData);
      NotificationService.success('Category updated successfully');
    } else {
      await categoryStore.createCategory(formData);
      NotificationService.success('Category created successfully');
    }

    router.push('/categories');
  } catch (err: any) {
    error.value = isEditMode.value
      ? 'Failed to update category. Please try again.'
      : 'Failed to create category. Please try again.';
    NotificationService.error(error.value);
    console.error('Category form error:', err);
  } finally {
    formLoading.value = false;
  }
};

// Reset form
const onReset = () => {
  error.value = null;
};

// Load category data on component mount
onMounted(() => {
  loadCategory();
});
</script>
