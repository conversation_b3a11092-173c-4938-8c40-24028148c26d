import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import BudgetService, { 
  Budget, 
  BudgetRequest, 
  BudgetListParams 
} from 'src/services/budget.service'

export const useBudgetStore = defineStore('budget', () => {
  // State
  const budgets = ref<Budget[]>([])
  const activeBudgets = ref<Budget[]>([])
  const currentBudget = ref<Budget | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const pagination = ref({
    total: 0,
    page: 1,
    pageSize: 10
  })
  
  // Getters
  const getBudgetsByCategory = computed(() => {
    return (categoryId: number) => {
      return budgets.value.filter(budget => budget.category_id === categoryId)
    }
  })
  
  const getBudgetsByPeriod = computed(() => {
    return (period: string) => {
      return budgets.value.filter(budget => budget.period === period)
    }
  })
  
  const totalBudgetAmount = computed(() => {
    return budgets.value.reduce((sum, budget) => sum + budget.amount, 0)
  })
  
  const totalSpentAmount = computed(() => {
    return budgets.value.reduce((sum, budget) => sum + (budget.spent_amount || 0), 0)
  })
  
  const overallPercentage = computed(() => {
    if (totalBudgetAmount.value === 0) return 0
    return (totalSpentAmount.value / totalBudgetAmount.value) * 100
  })
  
  // Actions
  async function fetchBudgets(params?: BudgetListParams) {
    loading.value = true
    error.value = null
    
    try {
      const response = await BudgetService.getBudgets(params)
      budgets.value = response.items
      pagination.value = {
        total: response.total,
        page: response.page,
        pageSize: response.page_size
      }
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch budgets'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function fetchBudget(id: number) {
    loading.value = true
    error.value = null
    
    try {
      const budget = await BudgetService.getBudget(id)
      currentBudget.value = budget
      return budget
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch budget'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function fetchActiveBudgets() {
    loading.value = true
    error.value = null
    
    try {
      const budgets = await BudgetService.getActiveBudgets()
      activeBudgets.value = budgets
      return budgets
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch active budgets'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function createBudget(budgetData: BudgetRequest) {
    loading.value = true
    error.value = null
    
    try {
      const budget = await BudgetService.createBudget(budgetData)
      budgets.value.push(budget)
      return budget
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to create budget'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function updateBudget(id: number, budgetData: BudgetRequest) {
    loading.value = true
    error.value = null
    
    try {
      const budget = await BudgetService.updateBudget(id, budgetData)
      
      // Update the budget in the budgets array
      const index = budgets.value.findIndex(b => b.id === id)
      if (index !== -1) {
        budgets.value[index] = budget
      }
      
      // Update the budget in the activeBudgets array if it exists
      const activeIndex = activeBudgets.value.findIndex(b => b.id === id)
      if (activeIndex !== -1) {
        activeBudgets.value[activeIndex] = budget
      }
      
      // Update currentBudget if it's the same budget
      if (currentBudget.value?.id === id) {
        currentBudget.value = budget
      }
      
      return budget
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to update budget'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function deleteBudget(id: number) {
    loading.value = true
    error.value = null
    
    try {
      await BudgetService.deleteBudget(id)
      
      // Remove the budget from the budgets array
      budgets.value = budgets.value.filter(b => b.id !== id)
      
      // Remove the budget from the activeBudgets array if it exists
      activeBudgets.value = activeBudgets.value.filter(b => b.id !== id)
      
      // Clear currentBudget if it's the same budget
      if (currentBudget.value?.id === id) {
        currentBudget.value = null
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to delete budget'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  return {
    // State
    budgets,
    activeBudgets,
    currentBudget,
    loading,
    error,
    pagination,
    
    // Getters
    getBudgetsByCategory,
    getBudgetsByPeriod,
    totalBudgetAmount,
    totalSpentAmount,
    overallPercentage,
    
    // Actions
    fetchBudgets,
    fetchBudget,
    fetchActiveBudgets,
    createBudget,
    updateBudget,
    deleteBudget
  }
})

export default useBudgetStore
