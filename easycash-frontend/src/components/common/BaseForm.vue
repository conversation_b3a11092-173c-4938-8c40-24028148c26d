<template>
  <q-form
    ref="formRef"
    @submit="onSubmit"
    @reset="onReset"
    class="q-gutter-md"
  >
    <slot></slot>

    <div class="row justify-end q-mt-md">
      <q-btn
        v-if="showReset"
        label="Reset"
        type="reset"
        color="secondary"
        flat
        class="q-ml-sm"
        :disable="loading"
      />
      <q-btn
        :label="submitLabel"
        type="submit"
        :color="submitColor"
        :loading="loading"
        class="q-ml-sm"
      />
    </div>
  </q-form>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineProps } from 'vue';
import { QForm } from 'quasar';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  submitLabel: {
    type: String,
    default: 'Submit'
  },
  submitColor: {
    type: String,
    default: 'primary'
  },
  showReset: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['submit', 'reset']);

const formRef = ref<QForm | null>(null);

const onSubmit = async () => {
  const isValid = await formRef.value?.validate();
  if (isValid) {
    emit('submit');
  }
};

const onReset = () => {
  formRef.value?.reset();
  emit('reset');
};

// Expose methods to parent component
defineExpose({
  validate: async () => await formRef.value?.validate(),
  reset: () => formRef.value?.reset(),
  focus: () => formRef.value?.focus()
});
</script>
