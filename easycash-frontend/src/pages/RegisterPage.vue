<template>
  <div>
  <q-card-section class="q-px-lg">
    <q-form @submit="onSubmit" class="q-gutter-md">
      <q-input
        v-model="username"
        label="Username"
        outlined
        :rules="[val => !!val || 'Username is required']"
      >
        <template v-slot:prepend>
          <q-icon name="person" />
        </template>
      </q-input>

      <q-input
        v-model="email"
        type="email"
        label="Email"
        outlined
        :rules="[val => !!val || 'Email is required', isValidEmail]"
      >
        <template v-slot:prepend>
          <q-icon name="email" />
        </template>
      </q-input>

      <q-input
        v-model="password"
        :type="isPwd ? 'password' : 'text'"
        label="Password"
        outlined
        :rules="[
          val => !!val || 'Password is required',
          val => val.length >= 8 || 'Password must be at least 8 characters'
        ]"
      >
        <template v-slot:prepend>
          <q-icon name="lock" />
        </template>
        <template v-slot:append>
          <q-icon
            :name="isPwd ? 'visibility_off' : 'visibility'"
            class="cursor-pointer"
            @click="isPwd = !isPwd"
          />
        </template>
      </q-input>

      <q-input
        v-model="confirmPassword"
        :type="isPwdConfirm ? 'password' : 'text'"
        label="Confirm Password"
        outlined
        :rules="[
          val => !!val || 'Please confirm your password',
          val => val === password || 'Passwords do not match'
        ]"
      >
        <template v-slot:prepend>
          <q-icon name="lock" />
        </template>
        <template v-slot:append>
          <q-icon
            :name="isPwdConfirm ? 'visibility_off' : 'visibility'"
            class="cursor-pointer"
            @click="isPwdConfirm = !isPwdConfirm"
          />
        </template>
      </q-input>

      <q-checkbox v-model="termsAccepted" label="I agree to the terms and conditions" :rules="[(val: boolean) => !!val || 'You must agree to terms']" />

      <q-btn
        class="full-width q-py-sm"
        color="primary"
        label="Sign Up"
        rounded
        type="submit"
        :loading="loading"
      />
    </q-form>
  </q-card-section>

  <q-card-section class="text-center">
    <p class="q-ma-none">Already have an account?
      <router-link to="/login" class="text-primary">Sign In</router-link>
    </p>
  </q-card-section>
</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';

const $q = useQuasar();
const router = useRouter();

const username = ref('');
const email = ref('');
const password = ref('');
const confirmPassword = ref('');
const isPwd = ref(true);
const isPwdConfirm = ref(true);
const termsAccepted = ref(false);
const loading = ref(false);

const isValidEmail = (val: string) => {
  const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailPattern.test(val) || 'Invalid email address';
};

const onSubmit = async () => {
  loading.value = true;
  
  try {
    // Here you would typically make an API call to register the user
    // For now, we'll simulate successful registration after a delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Redirect to login page after successful registration
    await router.push('/login');
    
    $q.notify({
      color: 'positive',
      message: 'Registration successful! You can now login.',
      icon: 'check_circle'
    });
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Registration failed:', err);
    $q.notify({
      color: 'negative',
      message: 'Registration failed. Please try again.',
      icon: 'error'
    });
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
a {
  text-decoration: none;
}
</style> 