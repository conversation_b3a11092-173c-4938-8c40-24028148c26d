核心功能模块
基础记账

快速记录：支持金额/分类/日期/备注/图片附件（小票）

交易类型：支出/收入/转账（跨账户）

智能填充：常用分类记忆（如自动填充"星巴克 → 餐饮"）

周期账单：自动生成房租、水电费等固定收支

分类管理

预设分类：餐饮/交通/住房/娱乐/医疗等（可自定义）

多级标签：支持"餐饮-下午茶"层级结构

图标库：100+分类图标可选

账户管理

多账户支持：现金/银行卡/支付宝/微信/信用卡

余额同步：手动或关联银行 API 自动更新（需安全认证）

预算系统

分类预算：为"餐饮"设置月预算 2000 元

动态预警：消费达 80%时推送提醒

结余结转：未用完预算可累积到下月

数据可视化

消费趋势图：按周/月/年对比分析

分类占比环形图：点击钻取子分类

地理热力图：显示消费地点分布（需开启定位）

二、技术架构
前端(vue)

框架：Quasar(v2) + TypeScript

图表库：移动端优化

本地缓存：

后端（可选云同步版）(go)

框架：

数据库：PostgreSQL（JSONB 存储动态字段）

安全方案：JWT + 交易记录 AES256 加密

特色技术

OCR 识别：Tesseract.js 实现小票自动识别

语音记账：集成科大讯飞语音转文本

智能预测：LSTM 模型分析周期性消费

三、交互设计亮点
时间轴视图

滑动时间轴快速定位日期

长按记录拖拽修改日期

场景化记账

旅游模式：自动记录地点+货币转换

聚餐 AA：自动拆分金额并生成催款链接

手势操作

左滑归档账单

双指捏合切换周/月视图

暗黑模式

根据系统自动切换

自定义主题色

四、数据安全方案
本地优先：所有数据优先存储于设备本地

备份策略

自动备份

导出加密 CSV 文件

隐私保护

生物识别锁定（Face ID/Touch ID）

敏感信息脱敏显示

五、扩展功能规划
版本 功能
1.0 基础记账+分类统计
2.0 多账户管理+预算系统
3.0 语音/拍照记账+数据同步
Pro 版 财务健康评分+投资跟踪
