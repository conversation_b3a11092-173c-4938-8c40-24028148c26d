<template>
  <div class="page-header q-mb-md">
    <div class="row items-center justify-between">
      <div class="col-auto">
        <div class="row items-center">
          <q-btn
            v-if="showBackButton"
            icon="arrow_back"
            flat
            round
            dense
            color="primary"
            class="q-mr-sm"
            @click="goBack"
          />
          <div class="text-h5 q-my-none">{{ title }}</div>
          <div v-if="subtitle" class="text-subtitle1 text-grey q-ml-md">
            {{ subtitle }}
          </div>
        </div>
      </div>
      <div class="col-auto">
        <slot name="actions"></slot>
      </div>
    </div>
    <q-separator class="q-mt-sm" />
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  showBackButton: {
    type: Boolean,
    default: false
  }
});

const router = useRouter();

const goBack = () => {
  router.back();
};
</script>
<script lang="ts">
export default {
  name: 'PageHeader'
};
</script>