<template>
  <div v-if="loading" class="loading-overlay">
    <q-spinner
      :color="color"
      :size="size"
      :thickness="thickness"
    />
    <div v-if="message" class="q-mt-sm text-body1">{{ message }}</div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';

defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  color: {
    type: String,
    default: 'primary'
  },
  size: {
    type: String,
    default: '50px'
  },
  thickness: {
    type: Number,
    default: 5
  },
  message: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
</style>
