import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import AccountService, { 
  Account, 
  AccountRequest, 
  AccountListParams,
  PaginatedResponse
} from 'src/services/account.service'

export const useAccountStore = defineStore('account', () => {
  // State
  const accounts = ref<Account[]>([])
  const currentAccount = ref<Account | null>(null)
  const defaultAccount = ref<Account | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const pagination = ref({
    total: 0,
    page: 1,
    pageSize: 10
  })
  
  // Getters
  const totalBalance = computed(() => {
    return accounts.value.reduce((sum, account) => sum + account.balance, 0)
  })
  
  const accountsByType = computed(() => {
    const result: Record<string, Account[]> = {}
    accounts.value.forEach(account => {
      if (!result[account.type]) {
        result[account.type] = []
      }
      result[account.type].push(account)
    })
    return result
  })
  
  // Actions
  async function fetchAccounts(params?: AccountListParams) {
    loading.value = true
    error.value = null
    
    try {
      const response = await AccountService.getAccounts(params)
      accounts.value = response.items
      pagination.value = {
        total: response.total,
        page: response.page,
        pageSize: response.page_size
      }
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch accounts'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function fetchAccount(id: number) {
    loading.value = true
    error.value = null
    
    try {
      const account = await AccountService.getAccount(id)
      currentAccount.value = account
      return account
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch account'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function fetchDefaultAccount() {
    loading.value = true
    error.value = null
    
    try {
      const account = await AccountService.getDefaultAccount()
      defaultAccount.value = account
      return account
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch default account'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function createAccount(accountData: AccountRequest) {
    loading.value = true
    error.value = null
    
    try {
      const account = await AccountService.createAccount(accountData)
      accounts.value.push(account)
      
      // If this is the default account, update defaultAccount
      if (account.is_default) {
        defaultAccount.value = account
      }
      
      return account
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to create account'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function updateAccount(id: number, accountData: AccountRequest) {
    loading.value = true
    error.value = null
    
    try {
      const account = await AccountService.updateAccount(id, accountData)
      
      // Update the account in the accounts array
      const index = accounts.value.findIndex(a => a.id === id)
      if (index !== -1) {
        accounts.value[index] = account
      }
      
      // Update currentAccount if it's the same account
      if (currentAccount.value?.id === id) {
        currentAccount.value = account
      }
      
      // Update defaultAccount if needed
      if (account.is_default) {
        defaultAccount.value = account
      } else if (defaultAccount.value?.id === id) {
        defaultAccount.value = null
      }
      
      return account
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to update account'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function deleteAccount(id: number) {
    loading.value = true
    error.value = null
    
    try {
      await AccountService.deleteAccount(id)
      
      // Remove the account from the accounts array
      accounts.value = accounts.value.filter(a => a.id !== id)
      
      // Clear currentAccount if it's the same account
      if (currentAccount.value?.id === id) {
        currentAccount.value = null
      }
      
      // Clear defaultAccount if it's the same account
      if (defaultAccount.value?.id === id) {
        defaultAccount.value = null
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to delete account'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  return {
    // State
    accounts,
    currentAccount,
    defaultAccount,
    loading,
    error,
    pagination,
    
    // Getters
    totalBalance,
    accountsByType,
    
    // Actions
    fetchAccounts,
    fetchAccount,
    fetchDefaultAccount,
    createAccount,
    updateAccount,
    deleteAccount
  }
})

export default useAccountStore
