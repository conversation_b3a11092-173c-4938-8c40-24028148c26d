import { Notify } from 'quasar'
import type { AxiosError } from 'axios'
import { API_ERROR_CODES } from 'src/types/api/response'
import type { APIErrorResponse } from 'src/types/api/response'

// 检查是否为网络错误
export function isNetworkError(error: unknown): error is AxiosError {
  return (error as AxiosError).isAxiosError === true
}

// 检查是否为API错误响应
export function isAPIError(error: unknown): error is APIErrorResponse {
  return typeof (error as APIErrorResponse)?.code === 'number' &&
    typeof (error as APIErrorResponse)?.message === 'string'
}

// 获取错误消息
export function getErrorMessage(error: unknown): string {
  if (isAPIError(error)) {
    return error.message
  }

  if (isNetworkError(error)) {
    if (error.response?.data) {
      const apiError = error.response.data as APIErrorResponse
      return apiError.message || '请求失败'
    }
    return error.message || '网络错误'
  }

  if (error instanceof Error) {
    return error.message
  }

  return '未知错误'
}

// 处理API错误
export function handleAPIError(error: unknown) {
  const message = getErrorMessage(error)
  let type: 'negative' | 'warning' = 'negative'
  
  // 根据错误码设置不同的通知类型
  if (isAPIError(error)) {
    switch (error.code) {
      case API_ERROR_CODES.VALIDATION_ERROR:
        type = 'warning'
        break
      case API_ERROR_CODES.UNAUTHORIZED:
        // 处理未授权错误，可以在这里调用登出逻辑
        break
    }
  }

  Notify.create({
    type,
    message,
    position: 'top',
    timeout: 3000
  })

  return Promise.reject(new Error(message))
} 