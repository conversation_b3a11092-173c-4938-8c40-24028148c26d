<template>
  <q-page class="flex flex-center">
    <q-card class="login-card">
      <q-card-section class="text-center">
        <div class="text-h5 q-mb-md">登录 EasyCash</div>
        <div class="text-subtitle2 text-grey">欢迎回来，请登录您的账号</div>
      </q-card-section>

      <q-card-section>
        <q-form @submit="handleSubmit" class="q-gutter-md">
          <q-input
            v-model="form.email"
            type="email"
            label="邮箱"
            :rules="[val => !!val || '请输入邮箱', val => validateEmail(val) || '请输入有效的邮箱']"
          />

          <q-input
            v-model="form.password"
            :type="showPassword ? 'text' : 'password'"
            label="密码"
            :rules="[val => !!val || '请输入密码']"
          >
            <template v-slot:append>
              <q-icon
                :name="showPassword ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="showPassword = !showPassword"
              />
            </template>
          </q-input>

          <div class="flex justify-between items-center q-mt-sm">
            <q-checkbox v-model="rememberMe" label="记住我" />
            <q-btn flat color="primary" label="忘记密码?" to="/auth/forgot-password" />
          </div>

          <q-btn
            type="submit"
            color="primary"
            class="full-width"
            label="登录"
            :loading="loading"
          />
        </q-form>
      </q-card-section>

      <q-card-section class="text-center q-pa-none">
        <q-separator class="q-my-md" />
        <div class="text-grey q-pa-md">
          还没有账号？
          <q-btn flat color="primary" label="立即注册" to="/auth/register" />
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useAuthStore } from 'src/stores/auth'

const router = useRouter()
const $q = useQuasar()
const authStore = useAuthStore()

const loading = ref(false)
const showPassword = ref(false)
const rememberMe = ref(false)

const form = reactive({
  email: '',
  password: ''
})

const validateEmail = (val: string) => {
  const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/
  return emailPattern.test(val)
}

const handleSubmit = async () => {
  loading.value = true
  try {
    await authStore.login(form.email, form.password)
    $q.notify({
      type: 'positive',
      message: '登录成功'
    })
    void router.push('/dashboard')
  } catch (err) {
    console.error('Login failed:', err)
    $q.notify({
      type: 'negative',
      message: '登录失败'
    })
  } finally {
    loading.value = false
  }
}

defineOptions({
  name: 'UserLoginPage'
})
</script>

<style lang="scss" scoped>
.login-card {
  width: 100%;
  max-width: 400px;
  padding: 20px;
}
</style>