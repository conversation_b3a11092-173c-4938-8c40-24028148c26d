<template>
  <q-layout view="hHh LpR fFf">
    <q-header elevated class="bg-primary text-white">
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
        />

        <q-toolbar-title>
          EasyCash
        </q-toolbar-title>

        <q-btn-dropdown flat icon="person">
          <q-list>
            <q-item clickable v-close-popup @click="handleProfile">
              <q-item-section>
                <q-item-label>个人信息</q-item-label>
              </q-item-section>
            </q-item>
            <q-item clickable v-close-popup @click="handleLogout">
              <q-item-section>
                <q-item-label>退出登录</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-btn-dropdown>
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
      class="bg-grey-1"
    >
      <q-list>
        <q-item-label header>导航菜单</q-item-label>

        <q-item
          v-for="nav in navigation"
          :key="nav.path"
          :to="nav.path"
          clickable
          v-ripple
        >
          <q-item-section avatar>
            <q-icon :name="nav.icon" />
          </q-item-section>
          <q-item-section>
            {{ nav.label }}
          </q-item-section>
        </q-item>
      </q-list>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from 'src/stores/auth.store'
import { useQuasar } from 'quasar'

const $q = useQuasar()
const router = useRouter()
const authStore = useAuthStore()

const leftDrawerOpen = ref(false)
const navigation = [
  { label: '仪表盘', icon: 'dashboard', path: '/dashboard' },
  { label: '交易记录', icon: 'receipt_long', path: '/transactions' },
  { label: '账户管理', icon: 'account_balance_wallet', path: '/accounts' },
  { label: '预算管理', icon: 'savings', path: '/budgets' },
  { label: '分类管理', icon: 'category', path: '/categories' },
  { label: '统计分析', icon: 'analytics', path: '/statistics' },
  { label: '设置', icon: 'settings', path: '/settings' }
]

const toggleLeftDrawer = () => {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

const handleProfile = () => {
  void router.push('/profile')
}

const handleLogout = () => {
  try {
    void authStore.logout()
    void router.push('/auth/login')
    $q.notify({
      type: 'positive',
      message: '已成功退出登录'
    })
  } catch (err) {
    console.error('Logout failed:', err)
    $q.notify({
      type: 'negative',
      message: '退出登录失败'
    })
  }
}
</script>

<style lang="scss" scoped>
.q-toolbar {
  min-height: 60px;
}
</style> 