---
description: 
globs: 
alwaysApply: true
---
# EasyCash API 集成规范

## 前端 API 集成

### Axios 配置

1. 基础配置
```typescript
// boot/axios.ts
import axios from 'axios';
import { useAuthStore } from '@/stores/auth.store';

const api = axios.create({
  baseURL: process.env.API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    const authStore = useAuthStore();
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => response.data,
  error => {
    if (error.response?.status === 401) {
      // 处理token过期
      const authStore = useAuthStore();
      authStore.logout();
    }
    return Promise.reject(error);
  }
);

export default api;
```

2. API 类型定义
```typescript
// types/api/transaction.ts
export interface TransactionAPI {
  create(data: CreateTransactionRequest): Promise<Transaction>;
  get(id: number): Promise<Transaction>;
  list(params: TransactionListParams): Promise<TransactionListResponse>;
  update(id: number, data: UpdateTransactionRequest): Promise<Transaction>;
  delete(id: number): Promise<void>;
}

export interface CreateTransactionRequest {
  amount: number;
  category: string;
  date: string;
  description?: string;
}

export interface TransactionListParams {
  page?: number;
  pageSize?: number;
  startDate?: string;
  endDate?: string;
  category?: string;
}

export interface TransactionListResponse {
  items: Transaction[];
  total: number;
  page: number;
  pageSize: number;
}
```

3. API 实现
```typescript
// api/transaction.ts
import api from '@/boot/axios';
import type { TransactionAPI, CreateTransactionRequest, TransactionListParams } from '@/types/api';

export const transactionAPI: TransactionAPI = {
  async create(data) {
    return api.post('/transactions', data);
  },
  
  async get(id) {
    return api.get(`/transactions/${id}`);
  },
  
  async list(params) {
    return api.get('/transactions', { params });
  },
  
  async update(id, data) {
    return api.put(`/transactions/${id}`, data);
  },
  
  async delete(id) {
    return api.delete(`/transactions/${id}`);
  }
};
```

### 错误处理

1. 全局错误处理
```typescript
// utils/error.ts
import { Notify } from 'quasar';

export function handleAPIError(error: any) {
  const message = error.response?.data?.message || '操作失败';
  
  Notify.create({
    type: 'negative',
    message
  });
  
  return Promise.reject(error);
}
```

2. 业务逻辑中的错误处理
```typescript
// composables/useTransaction.ts
export function useTransaction() {
  const loading = ref(false);
  
  async function createTransaction(data: CreateTransactionRequest) {
    loading.value = true;
    try {
      const result = await transactionAPI.create(data);
      Notify.create({
        type: 'positive',
        message: '创建成功'
      });
      return result;
    } catch (error) {
      handleAPIError(error);
    } finally {
      loading.value = false;
    }
  }
  
  return {
    loading,
    createTransaction
  };
}
```

## 后端 API 实现

### 路由定义

```go
// internal/api/router.go
func SetupRouter(r *gin.Engine) {
    api := r.Group("/api")
    {
        // 公开接口
        public := api.Group("")
        {
            public.POST("/auth/login", handler.Login)
            public.POST("/auth/register", handler.Register)
        }
        
        // 需要认证的接口
        protected := api.Group("").Use(middleware.Auth())
        {
            // 交易相关接口
            transactions := protected.Group("/transactions")
            {
                transactions.POST("", handler.CreateTransaction)
                transactions.GET("", handler.ListTransactions)
                transactions.GET("/:id", handler.GetTransaction)
                transactions.PUT("/:id", handler.UpdateTransaction)
                transactions.DELETE("/:id", handler.DeleteTransaction)
            }
        }
    }
}
```

### 请求处理

1. 请求验证
```go
// internal/model/transaction.go
type CreateTransactionRequest struct {
    Amount      float64   `json:"amount" binding:"required"`
    Category    string    `json:"category" binding:"required"`
    Date        time.Time `json:"date" binding:"required"`
    Description string    `json:"description"`
}

func (r *CreateTransactionRequest) Validate() error {
    if r.Amount <= 0 {
        return errors.New("amount must be positive")
    }
    return nil
}
```

2. 处理器实现
```go
// internal/api/transaction/handler.go
func (h *Handler) Create(c *gin.Context) {
    var req CreateTransactionRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.BadRequest(c, err)
        return
    }
    
    if err := req.Validate(); err != nil {
        response.BadRequest(c, err)
        return
    }
    
    userID := middleware.GetUserID(c)
    transaction, err := h.service.Create(c.Request.Context(), userID, req)
    if err != nil {
        response.Error(c, err)
        return
    }
    
    response.Success(c, transaction)
}
```

### 响应格式

```go
// internal/pkg/response/response.go
type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

func Success(c *gin.Context, data interface{}) {
    c.JSON(http.StatusOK, Response{
        Code:    0,
        Message: "success",
        Data:    data,
    })
}

func Error(c *gin.Context, err error) {
    var code int
    var message string
    
    switch e := err.(type) {
    case *ErrorResponse:
        code = e.Code
        message = e.Message
    default:
        code = -1
        message = err.Error()
    }
    
    c.JSON(http.StatusOK, Response{
        Code:    code,
        Message: message,
    })
}
```

### 错误处理

```go
// internal/pkg/errors/errors.go
type ErrorResponse struct {
    Code    int
    Message string
}

func (e *ErrorResponse) Error() string {
    return e.Message
}

var (
    ErrInvalidRequest = &ErrorResponse{Code: 400, Message: "invalid request"}
    ErrUnauthorized   = &ErrorResponse{Code: 401, Message: "unauthorized"}
    ErrNotFound       = &ErrorResponse{Code: 404, Message: "resource not found"}
)
```

### 认证中间件

```go
// internal/middleware/auth.go
func Auth() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := c.GetHeader("Authorization")
        if token == "" {
            response.Unauthorized(c, errors.ErrUnauthorized)
            c.Abort()
            return
        }
        
        // 验证token
        claims, err := jwt.ValidateToken(token)
        if err != nil {
            response.Unauthorized(c, err)
            c.Abort()
            return
        }
        
        // 设置用户信息到上下文
        c.Set("userID", claims.UserID)
        c.Next()
    }
}
``` 