// 这是一个占位文件，稍后将实现完整的预算存储功能

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { api } from 'src/boot/axios';

export interface Budget {
  id: number;
  name: string;
  amount: number;
  spent: number;
  categoryId?: number;
  startDate: string;
  endDate: string;
  period: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'custom';
  createdAt: string;
  updatedAt: string;
}

export const useBudgetsStore = defineStore('budgets', () => {
  // 状态
  const budgets = ref<Budget[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Getters
  const activeBudgets = computed(() => {
    const now = new Date();
    return budgets.value.filter(budget => {
      const startDate = new Date(budget.startDate);
      const endDate = new Date(budget.endDate);
      return startDate <= now && now <= endDate;
    });
  });

  const completedBudgets = computed(() => {
    const now = new Date();
    return budgets.value.filter(budget => {
      const endDate = new Date(budget.endDate);
      return endDate < now;
    });
  });

  const budgetProgress = computed(() => 
    budgets.value.map(budget => ({
      ...budget,
      percentage: Math.min(100, (budget.spent / budget.amount) * 100),
      remaining: budget.amount - budget.spent
    }))
  );

  // Actions
  const fetchBudgets = async () => {
    loading.value = true;
    error.value = null;

    try {
      const response = await api.get('/budgets');
      budgets.value = response.data;
      return budgets.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取预算失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const addBudget = async (budget: Omit<Budget, 'id' | 'spent' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await api.post('/budgets', budget);
      budgets.value.push(response.data);
      return response.data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建预算失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateBudget = async (id: number, data: Partial<Budget>) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await api.put(`/budgets/${id}`, data);
      const index = budgets.value.findIndex(b => b.id === id);
      if (index !== -1) {
        budgets.value[index] = response.data;
      }
      return response.data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新预算失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const deleteBudget = async (id: number) => {
    loading.value = true;
    error.value = null;

    try {
      await api.delete(`/budgets/${id}`);
      budgets.value = budgets.value.filter(b => b.id !== id);
      return true;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除预算失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    budgets,
    loading,
    error,
    activeBudgets,
    completedBudgets,
    budgetProgress,
    fetchBudgets,
    addBudget,
    updateBudget,
    deleteBudget
  };
});
