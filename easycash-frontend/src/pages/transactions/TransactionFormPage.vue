<template>
  <q-page padding>
    <page-header
      :title="mode === 'create' ? 'Create Transaction' : 'Edit Transaction'"
      show-back-button
    />
    
    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading && !formLoading" message="Loading transaction data..." />
    
    <q-card>
      <q-card-section>
        <transaction-form
          ref="formRef"
          :transaction="transaction"
          :loading="formLoading"
          :mode="mode"
          :initial-account-id="initialAccountId"
          :initial-category-id="initialCategoryId"
          @submit="onSubmit"
          @reset="onReset"
        />
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import TransactionForm from 'src/components/transactions/TransactionForm.vue';
import { useTransactionStore } from 'src/stores/transaction.store';
import { TransactionRequest } from 'src/services/transaction.service';
import NotificationService from 'src/services/notification.service';

const props = defineProps({
  id: {
    type: Number,
    default: 0
  },
  mode: {
    type: String,
    default: 'create',
    validator: (value: string) => ['create', 'edit'].includes(value)
  }
});

const router = useRouter();
const route = useRoute();
const transactionStore = useTransactionStore();

const formRef = ref<InstanceType<typeof TransactionForm> | null>(null);
const loading = ref(false);
const formLoading = ref(false);
const error = ref<string | null>(null);
const transaction = ref(transactionStore.currentTransaction);

// Computed properties
const isEditMode = computed(() => props.mode === 'edit');
const transactionId = computed(() => props.id || Number(route.params.id));

// Get initial account and category IDs from query parameters
const initialAccountId = computed(() => {
  const accountId = route.query.account_id;
  return accountId ? Number(accountId) : 0;
});

const initialCategoryId = computed(() => {
  const categoryId = route.query.category_id;
  return categoryId ? Number(categoryId) : 0;
});

// Load transaction data if in edit mode
const loadTransaction = async () => {
  if (!isEditMode.value || !transactionId.value) return;
  
  loading.value = true;
  error.value = null;
  
  try {
    await transactionStore.get(transactionId.value);
    transaction.value = transactionStore.currentTransaction;
  } catch (err: any) {
    error.value = 'Failed to load transaction. Please try again.';
    NotificationService.error('Failed to load transaction');
    console.error('Load transaction error:', err);
  } finally {
    loading.value = false;
  }
};

// Submit form
const onSubmit = async (formData: TransactionRequest) => {
  formLoading.value = true;
  error.value = null;
  
  try {
    if (isEditMode.value && transactionId.value) {
      await transactionStore.update(transactionId.value, formData);
      NotificationService.success('Transaction updated successfully');
    } else {
      await transactionStore.create(formData);
      NotificationService.success('Transaction created successfully');
    }
    
    router.push('/transactions');
  } catch (err: any) {
    error.value = isEditMode.value
      ? 'Failed to update transaction. Please try again.'
      : 'Failed to create transaction. Please try again.';
    NotificationService.error(error.value);
    console.error('Transaction form error:', err);
  } finally {
    formLoading.value = false;
  }
};

// Reset form
const onReset = () => {
  error.value = null;
};

// Load transaction data on component mount
onMounted(() => {
  loadTransaction();
});
</script>
