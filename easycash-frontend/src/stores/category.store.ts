import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import CategoryService, { 
  Category, 
  CategoryRequest, 
  CategoryListParams 
} from 'src/services/category.service'

export const useCategoryStore = defineStore('category', () => {
  // State
  const categories = ref<Category[]>([])
  const categoryHierarchy = ref<Category[]>([])
  const currentCategory = ref<Category | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const pagination = ref({
    total: 0,
    page: 1,
    pageSize: 50
  })
  
  // Getters
  const expenseCategories = computed(() => {
    return categories.value.filter(category => category.type === 'expense')
  })
  
  const incomeCategories = computed(() => {
    return categories.value.filter(category => category.type === 'income')
  })
  
  const expenseCategoryHierarchy = computed(() => {
    return categoryHierarchy.value.filter(category => category.type === 'expense')
  })
  
  const incomeCategoryHierarchy = computed(() => {
    return categoryHierarchy.value.filter(category => category.type === 'income')
  })
  
  const getCategoryById = computed(() => {
    return (id: number) => categories.value.find(category => category.id === id)
  })
  
  // Actions
  async function fetchCategories(params?: CategoryListParams) {
    loading.value = true
    error.value = null
    
    try {
      const response = await CategoryService.getCategories(params)
      categories.value = response.items
      pagination.value = {
        total: response.total,
        page: response.page,
        pageSize: response.page_size
      }
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch categories'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function fetchCategory(id: number) {
    loading.value = true
    error.value = null
    
    try {
      const category = await CategoryService.getCategory(id)
      currentCategory.value = category
      return category
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch category'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function fetchCategoryWithChildren(id: number) {
    loading.value = true
    error.value = null
    
    try {
      const category = await CategoryService.getCategoryWithChildren(id)
      currentCategory.value = category
      return category
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch category with children'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function fetchCategoryHierarchy(type?: string) {
    loading.value = true
    error.value = null
    
    try {
      const hierarchy = await CategoryService.getCategoryHierarchy(type)
      categoryHierarchy.value = hierarchy
      return hierarchy
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch category hierarchy'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function createCategory(categoryData: CategoryRequest) {
    loading.value = true
    error.value = null
    
    try {
      const category = await CategoryService.createCategory(categoryData)
      categories.value.push(category)
      
      // Refresh hierarchy if needed
      if (!categoryData.parent_id) {
        await fetchCategoryHierarchy()
      }
      
      return category
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to create category'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function updateCategory(id: number, categoryData: CategoryRequest) {
    loading.value = true
    error.value = null
    
    try {
      const category = await CategoryService.updateCategory(id, categoryData)
      
      // Update the category in the categories array
      const index = categories.value.findIndex(c => c.id === id)
      if (index !== -1) {
        categories.value[index] = category
      }
      
      // Update currentCategory if it's the same category
      if (currentCategory.value?.id === id) {
        currentCategory.value = category
      }
      
      // Refresh hierarchy if needed
      if (categoryData.parent_id !== undefined) {
        await fetchCategoryHierarchy()
      }
      
      return category
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to update category'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function deleteCategory(id: number) {
    loading.value = true
    error.value = null
    
    try {
      await CategoryService.deleteCategory(id)
      
      // Remove the category from the categories array
      categories.value = categories.value.filter(c => c.id !== id)
      
      // Clear currentCategory if it's the same category
      if (currentCategory.value?.id === id) {
        currentCategory.value = null
      }
      
      // Refresh hierarchy
      await fetchCategoryHierarchy()
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to delete category'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  return {
    // State
    categories,
    categoryHierarchy,
    currentCategory,
    loading,
    error,
    pagination,
    
    // Getters
    expenseCategories,
    incomeCategories,
    expenseCategoryHierarchy,
    incomeCategoryHierarchy,
    getCategoryById,
    
    // Actions
    fetchCategories,
    fetchCategory,
    fetchCategoryWithChildren,
    fetchCategoryHierarchy,
    createCategory,
    updateCategory,
    deleteCategory
  }
})

export default useCategoryStore
