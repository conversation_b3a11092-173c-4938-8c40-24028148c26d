<template>
  <q-page padding>
    <div class="row q-col-gutter-md">
      <!-- 筛选器 -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="row q-col-gutter-md items-center">
              <div class="col-12 col-sm-3">
                <q-input
                  v-model="filters.search"
                  dense
                  outlined
                  label="搜索"
                  clearable
                  @update:model-value="onFilterChange"
                >
                  <template v-slot:append>
                    <q-icon name="search" />
                  </template>
                </q-input>
              </div>
              <div class="col-12 col-sm-3">
                <q-select
                  v-model="filters.type"
                  :options="typeOptions"
                  dense
                  outlined
                  label="类型"
                  clearable
                  emit-value
                  map-options
                  @update:model-value="onFilterChange"
                />
              </div>
              <div class="col-12 col-sm-3">
                <q-select
                  v-model="filters.category"
                  :options="categoryOptions"
                  dense
                  outlined
                  label="分类"
                  clearable
                  emit-value
                  map-options
                  @update:model-value="onFilterChange"
                />
              </div>
              <div class="col-12 col-sm-3">
                <q-btn
                  color="primary"
                  icon="add"
                  label="新增交易"
                  class="full-width"
                  to="/transactions/new"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- 交易列表 -->
      <div class="col-12">
        <q-card>
          <q-table
            :rows="transactions"
            :columns="columns"
            row-key="id"
            :loading="loading"
            v-model:pagination="pagination"
            @request="onRequest"
            binary-state-sort
          >
            <!-- 金额列自定义 -->
            <template v-slot:body-cell-amount="props">
              <q-td :props="props">
                <span :class="{ 'text-negative': props.row.type === 'expense', 'text-positive': props.row.type === 'income' }">
                  {{ props.row.type === 'expense' ? '-' : '+' }}¥ {{ props.row.amount }}
                </span>
              </q-td>
            </template>

            <!-- 操作列 -->
            <template v-slot:body-cell-actions="props">
              <q-td :props="props">
                <q-btn
                  flat
                  round
                  dense
                  color="primary"
                  icon="edit"
                  @click="editTransaction(props.row.id)"
                >
                  <q-tooltip>编辑</q-tooltip>
                </q-btn>
                <q-btn
                  flat
                  round
                  dense
                  color="negative"
                  icon="delete"
                  @click="confirmDelete(props.row.id)"
                >
                  <q-tooltip>删除</q-tooltip>
                </q-btn>
              </q-td>
            </template>
          </q-table>
        </q-card>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <q-dialog v-model="deleteDialog.show" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="warning" text-color="white" />
          <span class="q-ml-sm">确定要删除这条交易记录吗？</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="取消" color="primary" v-close-popup />
          <q-btn flat label="删除" color="negative" @click="() => deleteTransaction(deleteDialog.transactionId)" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRouter } from 'vue-router'
import { useTransactionStore } from 'src/stores/transaction.store'
import { useCategoryStore } from 'src/stores/category.store'
import type { Transaction } from 'src/types/models/transaction'
// import { formatDate, formatAmount } from 'src/utils/format'

const $q = useQuasar()
const router = useRouter()
const transactionStore = useTransactionStore()
const categoryStore = useCategoryStore()

defineOptions({
  name: 'TransactionListPage'
})

// 筛选条件
const filters = reactive({
  search: '',
  type: null as 'income' | 'expense' | null,
  category: null as string | null,
  startDate: '',
  endDate: ''
})

// 选项
const typeOptions = [
  { label: '支出', value: 'expense' as const },
  { label: '收入', value: 'income' as const }
]

const categoryOptions = [
  { label: '餐饮', value: 'food' },
  { label: '交通', value: 'transport' },
  { label: '购物', value: 'shopping' },
  { label: '娱乐', value: 'entertainment' },
  { label: '工资', value: 'salary' }
]

// 表格配置
const columns = [
  {
    name: 'date',
    required: true,
    label: '日期',
    align: 'left' as const,
    field: 'date',
    sortable: true
  },
  {
    name: 'description',
    required: true,
    label: '描述',
    align: 'left' as const,
    field: 'description'
  },
  {
    name: 'category',
    required: true,
    label: '分类',
    align: 'left' as const,
    field: 'category'
  },
  {
    name: 'amount',
    required: true,
    label: '金额',
    align: 'right' as const,
    field: 'amount',
    sortable: true
  },
  {
    name: 'actions',
    required: true,
    label: '操作',
    align: 'center' as const,
    field: 'actions'
  }
]

// 分页配置
const pagination = ref({
  sortBy: 'date',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0
})

// 状态
const loading = ref(false)
const transactions = ref<Transaction[]>([])

// 删除对话框状态
const deleteDialog = reactive({
  show: false,
  transactionId: null as number | null
})

// 加载数据
const loadTransactions = async () => {
  loading.value = true
  try {
    const { items, total } = await transactionStore.list({
      page: pagination.value.page,
      limit: pagination.value.rowsPerPage,
      search: filters.search,
      type: filters.type || undefined,
      category_id: filters.category ? Number(filters.category) : undefined,
      start_date: filters.startDate || undefined,
      end_date: filters.endDate || undefined
    })
    transactions.value = items
    pagination.value.rowsNumber = total
  } catch (err) {
    console.error('Failed to load transactions:', err)
    $q.notify({
      type: 'negative',
      message: '加载交易记录失败'
    })
  } finally {
    loading.value = false
  }
}

// 处理分页变化
const onRequest = async (props: { pagination: { page: number; rowsPerPage: number } }) => {
  const { page, rowsPerPage } = props.pagination
  pagination.value.page = page
  pagination.value.rowsPerPage = rowsPerPage
  await loadTransactions()
}

// 处理筛选变化
const onFilterChange = async () => {
  pagination.value.page = 1
  await loadTransactions()
}

// 删除交易
const deleteTransaction = async (id: number) => {
  try {
    await transactionStore.delete(id)
    $q.notify({
      type: 'positive',
      message: '删除成功'
    })
    await loadTransactions()
  } catch (err) {
    console.error('Failed to delete transaction:', err)
    $q.notify({
      type: 'negative',
      message: '删除失败'
    })
  }
}

// 确认删除
const confirmDelete = (id: number) => {
  deleteDialog.transactionId = id
  deleteDialog.show = true
}

// 编辑交易
const editTransaction = (id: number) => {
  void router.push(`/transactions/${id}/edit`)
}

// 初始化
onMounted(async () => {
  try {
    await Promise.all([
      loadTransactions(),
      categoryStore.fetchCategories()
    ])
  } catch (err) {
    console.error('Failed to initialize:', err)
  }
})
</script>

<style lang="scss" scoped>
.q-table__card {
  box-shadow: none;
}
</style>