<template>
  <q-page class="q-pa-md">
    <div class="row items-center q-mb-md">
      <div class="col">
        <div class="text-h5">Transactions</div>
        <div class="text-subtitle2 text-grey-7">Manage your income and expenses</div>
      </div>
      <div class="col-auto">
        <q-btn 
          color="primary" 
          icon="add" 
          label="New Transaction" 
          @click="openTransactionDialog()"
        />
      </div>
    </div>

    <!-- Filters Section -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-3">
            <q-select
              v-model="filters.type"
              :options="transactionTypes"
              label="Type"
              outlined
              dense
              emit-value
              map-options
              clearable
            />
          </div>
          <div class="col-12 col-md-3">
            <q-select
              v-model="filters.account"
              :options="accountOptions"
              label="Account"
              outlined
              dense
              emit-value
              map-options
              clearable
            />
          </div>
          <div class="col-12 col-md-3">
            <q-select
              v-model="filters.category"
              :options="categoryOptions"
              label="Category"
              outlined
              dense
              emit-value
              map-options
              clearable
            />
          </div>
          <div class="col-12 col-md-3">
            <q-input
              v-model="dateRangeModel"
              outlined
              dense
              label="Date Range"
              mask="date-range"
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                    <q-date v-model="filters.dateRange" range />
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>
        </div>
        <div class="row q-mt-md">
          <div class="col-12 col-md-9">
            <q-input
              v-model="filters.search"
              outlined
              dense
              label="Search transactions"
              placeholder="Search by description or amount"
              clearable
            >
              <template v-slot:append>
                <q-icon name="search" />
              </template>
            </q-input>
          </div>
          <div class="col-12 col-md-3 q-mt-sm-xs q-mt-md-none">
            <div class="row q-col-gutter-sm">
              <div class="col">
                <q-btn
                  color="primary"
                  outline
                  icon="filter_list"
                  label="Apply Filters"
                  class="full-width"
                  @click="applyFilters"
                />
              </div>
              <div class="col-auto">
                <q-btn
                  color="grey-7"
                  flat
                  icon="refresh"
                  @click="resetFilters"
                />
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Transactions Table -->
    <q-card>
      <q-table
        :rows="transactions"
        :columns="columns"
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        @request="onRequest"
        binary-state-sort
      >
        <!-- Custom header slots -->
        <template v-slot:header="props">
          <q-tr :props="props">
            <q-th v-for="col in props.cols" :key="col.name" :props="props">
              {{ col.label }}
            </q-th>
            <q-th auto-width>Actions</q-th>
          </q-tr>
        </template>
        
        <!-- Custom body slots -->
        <template v-slot:body="props">
          <q-tr :props="props">
            <q-td key="date" :props="props">
              {{ formatDate(props.row.date) }}
            </q-td>
            <q-td key="description" :props="props">
              <div class="row items-center">
                <q-icon :name="getCategoryIcon(props.row.category)" class="q-mr-sm" color="primary" />
                {{ props.row.description }}
              </div>
            </q-td>
            <q-td key="category" :props="props">
              <q-chip dense size="sm" :color="getCategoryColor(props.row.category)" text-color="white">
                {{ props.row.category }}
              </q-chip>
            </q-td>
            <q-td key="account" :props="props">
              {{ props.row.account }}
            </q-td>
            <q-td key="amount" :props="props" :class="props.row.amount < 0 ? 'text-negative' : 'text-positive'">
              {{ formatCurrency(props.row.amount) }}
            </q-td>
            <q-td auto-width>
              <q-btn flat round dense icon="edit" color="primary" @click="openTransactionDialog(props.row)" />
              <q-btn flat round dense icon="delete" color="negative" @click="confirmDelete(props.row)" />
            </q-td>
          </q-tr>
        </template>
        
        <!-- Loading state -->
        <template v-slot:loading>
          <q-inner-loading showing color="primary" />
        </template>
        
        <!-- No data -->
        <template v-slot:no-data>
          <div class="full-width row flex-center q-pa-md text-grey-7">
            <q-icon name="sentiment_dissatisfied" size="2rem" class="q-mr-sm" />
            No transactions found
          </div>
        </template>
      </q-table>
    </q-card>

    <!-- Transaction Form Dialog -->
    <q-dialog v-model="transactionDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section class="row items-center">
          <div class="text-h6">{{ editMode ? 'Edit Transaction' : 'New Transaction' }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section>
          <q-form @submit="saveTransaction" class="q-gutter-md">
            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-input
                  v-model="currentTransaction.description"
                  label="Description"
                  outlined
                  :rules="[val => !!val || 'Description is required']"
                />
              </div>
              
              <div class="col-12 col-md-6">
                <q-select
                  v-model="currentTransaction.type"
                  :options="transactionTypes"
                  label="Type"
                  outlined
                  emit-value
                  map-options
                  :rules="[val => !!val || 'Type is required']"
                />
              </div>
              
              <div class="col-12 col-md-6">
                <q-input
                  v-model="currentTransaction.amount"
                  label="Amount"
                  outlined
                  type="number"
                  :rules="[val => !!val || 'Amount is required']"
                >
                  <template v-slot:prepend>
                    <q-icon name="attach_money" />
                  </template>
                </q-input>
              </div>
              
              <div class="col-12 col-md-6">
                <q-select
                  v-model="currentTransaction.category"
                  :options="categoryOptions"
                  label="Category"
                  outlined
                  emit-value
                  map-options
                  :rules="[val => !!val || 'Category is required']"
                />
              </div>
              
              <div class="col-12 col-md-6">
                <q-select
                  v-model="currentTransaction.account"
                  :options="accountOptions"
                  label="Account"
                  outlined
                  emit-value
                  map-options
                  :rules="[val => !!val || 'Account is required']"
                />
              </div>
              
              <div class="col-12">
                <q-input
                  v-model="currentTransaction.date"
                  outlined
                  label="Date"
                  :rules="[val => !!val || 'Date is required']"
                >
                  <template v-slot:append>
                    <q-icon name="event" class="cursor-pointer">
                      <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                        <q-date v-model="currentTransaction.date" mask="YYYY-MM-DD" />
                      </q-popup-proxy>
                    </q-icon>
                  </template>
                </q-input>
              </div>
              
              <div class="col-12">
                <q-input
                  v-model="currentTransaction.notes"
                  label="Notes"
                  outlined
                  type="textarea"
                />
              </div>
            </div>
            
            <div class="row q-col-gutter-sm q-mt-md">
              <div class="col">
                <q-btn
                  color="primary"
                  label="Save"
                  type="submit"
                  class="full-width"
                />
              </div>
              <div class="col">
                <q-btn
                  color="grey-7"
                  label="Cancel"
                  v-close-popup
                  class="full-width"
                />
              </div>
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="negative" text-color="white" />
          <span class="q-ml-sm">Delete Transaction</span>
        </q-card-section>

        <q-card-section>
          Are you sure you want to delete this transaction?
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Delete" color="negative" @click="deleteTransaction" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useQuasar } from 'quasar';
import type { QTableColumn } from 'quasar';

// Define proper interfaces for our data structures
interface Transaction {
  id: number | null;
  description: string;
  type: string;
  category: string;
  account: string;
  amount: number;
  date: string;
  notes: string;
}

interface DateRange {
  from: string;
  to: string;
}

const $q = useQuasar();

// Table configuration
const columns: QTableColumn[] = [
  { name: 'date', label: 'Date', field: 'date', sortable: true },
  { name: 'description', label: 'Description', field: 'description', sortable: true },
  { name: 'category', label: 'Category', field: 'category', sortable: true },
  { name: 'account', label: 'Account', field: 'account', sortable: true },
  { name: 'amount', label: 'Amount', field: 'amount', sortable: true, align: 'right' }
];

const pagination = ref({
  sortBy: 'date',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0
});

// State variables
const loading = ref(false);
const transactions = ref<Transaction[]>([]);
const transactionDialog = ref(false);
const deleteDialog = ref(false);
const editMode = ref(false);
const currentTransaction = ref<Transaction>({
  id: null,
  description: '',
  type: '',
  category: '',
  account: '',
  amount: 0,
  date: '',
  notes: ''
});
const transactionToDelete = ref<Transaction | null>(null);

// Filter state
const filters = reactive({
  type: null as string | null,
  account: null as string | null,
  category: null as string | null,
  dateRange: { from: '', to: '' } as DateRange,
  search: ''
});

// 修改计算属性转换日期范围格式
const dateRangeModel = computed<string>({
  get: () => {
    // 返回空字符串，实际日期控制仍由q-date处理
    return '';
  },
  set: () => {
    // 空实现，我们会在q-date组件上直接绑定修改filters.dateRange
  }
});

// Options for dropdowns
const transactionTypes = [
  { label: 'Income', value: 'income' },
  { label: 'Expense', value: 'expense' }
];

const accountOptions = [
  { label: 'Main Account', value: 'Main Account' },
  { label: 'Savings', value: 'Savings' },
  { label: 'Credit Card', value: 'Credit Card' }
];

const categoryOptions = [
  { label: 'Food', value: 'Food' },
  { label: 'Transportation', value: 'Transportation' },
  { label: 'Housing', value: 'Housing' },
  { label: 'Entertainment', value: 'Entertainment' },
  { label: 'Shopping', value: 'Shopping' },
  { label: 'Health', value: 'Health' },
  { label: 'Education', value: 'Education' },
  { label: 'Salary', value: 'Salary' }
];

// Helper functions
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(value);
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getCategoryIcon = (category: string) => {
  const icons: Record<string, string> = {
    'Food': 'restaurant',
    'Transportation': 'directions_car',
    'Housing': 'home',
    'Entertainment': 'movie',
    'Shopping': 'shopping_bag',
    'Health': 'health_and_safety',
    'Education': 'school',
    'Salary': 'payments'
  };
  
  return icons[category] || 'category';
};

const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    'Food': 'red',
    'Transportation': 'blue',
    'Housing': 'teal',
    'Entertainment': 'purple',
    'Shopping': 'pink',
    'Health': 'green',
    'Education': 'amber',
    'Salary': 'positive'
  };
  
  return colors[category] || 'grey';
};

// CRUD operations
const fetchTransactions = async () => {
  loading.value = true;
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock data
    const mockData: Transaction[] = [
      { id: 1, description: 'Grocery Store', category: 'Food', amount: -85.20, date: '2023-09-01', account: 'Main Account', type: 'expense', notes: '' },
      { id: 2, description: 'Monthly Salary', category: 'Salary', amount: 2800.00, date: '2023-08-28', account: 'Main Account', type: 'income', notes: 'August salary' },
      { id: 3, description: 'Restaurant Dinner', category: 'Food', amount: -62.50, date: '2023-08-25', account: 'Credit Card', type: 'expense', notes: 'Dinner with friends' },
      { id: 4, description: 'Uber Ride', category: 'Transportation', amount: -18.75, date: '2023-08-22', account: 'Credit Card', type: 'expense', notes: '' },
      { id: 5, description: 'Amazon Purchase', category: 'Shopping', amount: -34.99, date: '2023-08-20', account: 'Main Account', type: 'expense', notes: 'New headphones' },
      { id: 6, description: 'Rent Payment', category: 'Housing', amount: -1200.00, date: '2023-08-05', account: 'Main Account', type: 'expense', notes: 'September rent' },
      { id: 7, description: 'Gym Membership', category: 'Health', amount: -50.00, date: '2023-08-03', account: 'Credit Card', type: 'expense', notes: 'Monthly fee' },
      { id: 8, description: 'Freelance Work', category: 'Salary', amount: 450.00, date: '2023-08-02', account: 'Savings', type: 'income', notes: 'Website project' }
    ];
    
    transactions.value = mockData;
    pagination.value.rowsNumber = mockData.length;
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Failed to load transactions:', err);
    $q.notify({
      color: 'negative',
      message: 'Failed to load transactions',
      icon: 'error'
    });
  } finally {
    loading.value = false;
  }
};

const openTransactionDialog = (transaction: Transaction | null = null) => {
  if (transaction) {
    // Edit mode
    currentTransaction.value = { ...transaction };
    editMode.value = true;
  } else {
    // Create mode
    currentTransaction.value = {
      id: null,
      description: '',
      type: 'expense',
      category: '',
      account: 'Main Account',
      amount: 0,
      date: new Date().toISOString().split('T')[0] || '',
      notes: ''
    };
    editMode.value = false;
  }
  
  transactionDialog.value = true;
};

const saveTransaction = async () => {
  // Adjust amount based on transaction type
  if (currentTransaction.value.type === 'expense' && currentTransaction.value.amount > 0) {
    currentTransaction.value.amount = -currentTransaction.value.amount;
  } else if (currentTransaction.value.type === 'income' && currentTransaction.value.amount < 0) {
    currentTransaction.value.amount = Math.abs(currentTransaction.value.amount);
  }
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (editMode.value) {
      // Update existing transaction
      const index = transactions.value.findIndex(t => t.id === currentTransaction.value.id);
      if (index !== -1) {
        transactions.value[index] = { ...currentTransaction.value };
      }
      
      $q.notify({
        color: 'positive',
        message: 'Transaction updated successfully',
        icon: 'check_circle'
      });
    } else {
      // Create new transaction
      const newId = Math.max(...transactions.value.map(t => t.id || 0), 0) + 1;
      const newTransaction: Transaction = {
        ...currentTransaction.value,
        id: newId
      };
      
      transactions.value.unshift(newTransaction);
      
      $q.notify({
        color: 'positive',
        message: 'Transaction created successfully',
        icon: 'check_circle'
      });
    }
    
    transactionDialog.value = false;
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error(editMode.value ? 'Failed to update transaction:' : 'Failed to create transaction:', err);
    $q.notify({
      color: 'negative',
      message: editMode.value ? 'Failed to update transaction' : 'Failed to create transaction',
      icon: 'error'
    });
  }
};

const confirmDelete = (transaction: Transaction) => {
  transactionToDelete.value = transaction;
  deleteDialog.value = true;
};

const deleteTransaction = async () => {
  if (!transactionToDelete.value) return;
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Remove from local array
    const index = transactions.value.findIndex(t => t.id === transactionToDelete.value?.id);
    if (index !== -1) {
      transactions.value.splice(index, 1);
    }
    
    $q.notify({
      color: 'positive',
      message: 'Transaction deleted successfully',
      icon: 'check_circle'
    });
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Failed to delete transaction:', err);
    $q.notify({
      color: 'negative',
      message: 'Failed to delete transaction',
      icon: 'error'
    });
  } finally {
    transactionToDelete.value = null;
  }
};

// 定义RequestProps接口
interface RequestProps {
  pagination: {
    page: number;
    rowsPerPage: number;
    sortBy: string;
    descending: boolean;
  }
}

// Filter operations
const applyFilters = () => {
  void fetchTransactions(); // In a real app, you would pass filters to the API
};

const resetFilters = () => {
  filters.type = null;
  filters.account = null;
  filters.category = null;
  filters.dateRange = { from: '', to: '' };
  filters.search = '';
  
  void fetchTransactions();
};

// Table operations
const onRequest = (props: RequestProps) => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;
  
  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;
  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;
  
  void fetchTransactions();
};

// Initialize
onMounted(() => {
  void fetchTransactions();
});
</script>

<style lang="scss" scoped>
// You can add custom styling here if needed
</style> 