package main

import (
	"fmt"
)

type Account struct {
	ID      int
	Name    string
	Balance float64
}

type AccountResponse struct {
	ID      int
	Name    string
	Balance float64
}

func (a *Account) ToResponse() AccountResponse {
	return AccountResponse{
		ID:      a.ID,
		Name:    a.Name,
		Balance: a.Balance,
	}
}

func main() {
	account := Account{
		ID:      1,
		Name:    "Test Account",
		Balance: 100.0,
	}

	// 正确的方法
	accountResponse := account.ToResponse()
	fmt.Printf("Account Response: %+v\n", accountResponse)
	fmt.Printf("Account Response Address: %p\n", &accountResponse)

	fmt.Println("Test completed successfully!")
}
