<template>
  <q-layout view="lHh Lpr lFf">
    <q-header elevated class="bg-primary text-white">
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
        />

        <q-toolbar-title>
          Easy Cash
        </q-toolbar-title>

        <q-btn flat round icon="account_circle" aria-label="User">
          <q-menu>
            <q-list style="min-width: 100px">
              <q-item clickable v-close-popup>
                <q-item-section>Profile</q-item-section>
              </q-item>
              <q-item clickable v-close-popup>
                <q-item-section>Settings</q-item-section>
              </q-item>
              <q-separator />
              <q-item clickable v-close-popup>
                <q-item-section>Logout</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </q-toolbar>
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      show-if-above
      bordered
      class="bg-grey-1"
    >
      <q-list>
        <q-item-label header class="text-grey-8">
          EasyCash Menu
        </q-item-label>

        <q-item
          v-for="link in menuLinks"
          :key="link.title"
          :to="link.route"
          clickable
          v-ripple
        >
          <q-item-section avatar>
            <q-icon :name="link.icon" />
          </q-item-section>
          <q-item-section>
            {{ link.title }}
          </q-item-section>
        </q-item>
      </q-list>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>

    <q-footer bordered class="bg-white text-primary">
      <q-tabs
        no-caps
        active-color="primary"
        indicator-color="primary"
        align="justify"
        narrow-indicator
      >
        <q-route-tab to="/" icon="home" label="Home" />
        <q-route-tab to="/transactions" icon="receipt_long" label="Records" />
        <q-route-tab to="/statistics" icon="insert_chart" label="Stats" />
        <q-route-tab to="/accounts" icon="account_balance_wallet" label="Accounts" />
      </q-tabs>
    </q-footer>
  </q-layout>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface MenuLink {
  title: string;
  icon: string;
  route: string;
}

const menuLinks: MenuLink[] = [
  {
    title: 'Dashboard',
    icon: 'dashboard',
    route: '/'
  },
  {
    title: 'Transactions',
    icon: 'receipt_long',
    route: '/transactions'
  },
  {
    title: 'Accounts',
    icon: 'account_balance_wallet',
    route: '/accounts'
  },
  {
    title: 'Categories',
    icon: 'category',
    route: '/categories'
  },
  {
    title: 'Budgets',
    icon: 'savings',
    route: '/budgets'
  },
  {
    title: 'Statistics',
    icon: 'insert_chart',
    route: '/statistics'
  }
];

const leftDrawerOpen = ref(false);

function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value;
}
</script>
