package model

import (
	"time"

	"gorm.io/gorm"
)

// Budget represents a budget for a specific category
type Budget struct {
	ID         uint      `json:"id" gorm:"primarykey"`
	UserID     uint      `json:"user_id" gorm:"not null;index"`
	CategoryID uint      `json:"category_id" gorm:"not null;index"`
	Amount     float64   `json:"amount" gorm:"type:decimal(10,2);not null"`
	Period     string    `json:"period" gorm:"size:20;not null"` // monthly, yearly, custom
	StartDate  time.Time `json:"start_date" gorm:"not null"`
	EndDate    time.Time `json:"end_date" gorm:"not null"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relations
	User     User     `json:"-" gorm:"foreignKey:UserID"`
	Category Category `json:"-" gorm:"foreignKey:CategoryID"`
}

// BudgetRequest is used for creating or updating a budget
type BudgetRequest struct {
	CategoryID uint      `json:"category_id" binding:"required"`
	Amount     float64   `json:"amount" binding:"required"`
	Period     string    `json:"period" binding:"required"`
	StartDate  time.Time `json:"start_date" binding:"required"`
	EndDate    time.Time `json:"end_date" binding:"required"`
}

// BudgetResponse is used for returning budget data
type BudgetResponse struct {
	ID         uint      `json:"id"`
	UserID     uint      `json:"user_id"`
	CategoryID uint      `json:"category_id"`
	Amount     float64   `json:"amount"`
	Period     string    `json:"period"`
	StartDate  time.Time `json:"start_date"`
	EndDate    time.Time `json:"end_date"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	
	// Calculated fields
	SpentAmount float64 `json:"spent_amount,omitempty"`
	Percentage  float64 `json:"percentage,omitempty"`
	
	// Embedded related data
	Category *CategoryResponse `json:"category,omitempty"`
}

// BudgetListParams is used for filtering budgets
type BudgetListParams struct {
	UserID     uint       `form:"user_id"`
	CategoryID uint       `form:"category_id"`
	Period     string     `form:"period"`
	StartDate  *time.Time `form:"start_date"`
	EndDate    *time.Time `form:"end_date"`
	Page       int        `form:"page,default=1"`
	Limit      int        `form:"limit,default=10"`
}

// ToResponse converts a Budget to BudgetResponse
func (b *Budget) ToResponse() BudgetResponse {
	return BudgetResponse{
		ID:         b.ID,
		UserID:     b.UserID,
		CategoryID: b.CategoryID,
		Amount:     b.Amount,
		Period:     b.Period,
		StartDate:  b.StartDate,
		EndDate:    b.EndDate,
		CreatedAt:  b.CreatedAt,
		UpdatedAt:  b.UpdatedAt,
	}
}

// ToResponseWithRelations converts a Budget to BudgetResponse with related data
func (b *Budget) ToResponseWithRelations() BudgetResponse {
	response := b.ToResponse()
	
	categoryResponse := b.Category.ToResponse()
	response.Category = &categoryResponse
	
	return response
}

// ToResponseWithStats converts a Budget to BudgetResponse with statistics
func (b *Budget) ToResponseWithStats(spentAmount float64) BudgetResponse {
	response := b.ToResponse()
	
	response.SpentAmount = spentAmount
	
	// Calculate percentage of budget used
	if b.Amount > 0 {
		response.Percentage = (spentAmount / b.Amount) * 100
	}
	
	return response
}
