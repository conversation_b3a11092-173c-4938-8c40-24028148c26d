import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import AuthService, { User as AuthUser } from 'src/services/auth.service'

export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  token?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
}

export interface UpdateProfileRequest {
  username: string
  email: string
  avatar?: string
}

export interface ChangePasswordRequest {
  old_password: string
  new_password: string
}

export const useAuthStore = defineStore('auth', () => {
  const router = useRouter()

  // State
  const user = ref<User | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => AuthService.isAuthenticated())
  const currentUser = computed(() => user.value)

  // Login
  const login = async (credentials: LoginRequest) => {
    loading.value = true
    error.value = null

    try {
      user.value = await AuthService.login(credentials)
      void router.push('/dashboard')
      return user.value
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Login failed'
      console.error('Login failed:', err)
      throw error.value
    } finally {
      loading.value = false
    }
  }

  // Register
  const register = async (userData: RegisterRequest) => {
    loading.value = true
    error.value = null

    try {
      user.value = await AuthService.register(userData)
      void router.push('/dashboard')
      return user.value
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Registration failed'
      console.error('Registration failed:', err)
      throw error.value
    } finally {
      loading.value = false
    }
  }

  // Logout
  const logout = () => {
    AuthService.logout()
    user.value = null
    void router.push('/auth/login')
  }

  // Get user profile
  const fetchUserInfo = async () => {
    if (!isAuthenticated.value) return null

    loading.value = true
    error.value = null

    try {
      user.value = await AuthService.getProfile()
      return user.value
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch profile'
      console.error('Failed to fetch user info:', err)
      throw error.value
    } finally {
      loading.value = false
    }
  }

  // Update profile
  const updateProfile = async (profileData: UpdateProfileRequest) => {
    loading.value = true
    error.value = null

    try {
      user.value = await AuthService.updateProfile(profileData)
      return user.value
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to update profile'
      console.error('Failed to update profile:', err)
      throw error.value
    } finally {
      loading.value = false
    }
  }

  // Change password
  const changePassword = async (passwordData: ChangePasswordRequest) => {
    loading.value = true
    error.value = null

    try {
      await AuthService.changePassword(passwordData)
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to change password'
      console.error('Failed to change password:', err)
      throw error.value
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    user,
    loading,
    error,

    // Getters
    isAuthenticated,
    currentUser,

    // Actions
    login,
    register,
    logout,
    fetchUserInfo,
    updateProfile,
    changePassword
  }
})