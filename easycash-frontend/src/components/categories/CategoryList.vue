<template>
  <div class="category-list">
    <q-list separator>
      <q-item
        v-for="category in categories"
        :key="category.id"
        clickable
        @click="$emit('select', category.id)"
      >
        <q-item-section avatar>
          <q-avatar :color="category.color || 'primary'" text-color="white">
            <q-icon :name="category.icon || 'category'" />
          </q-avatar>
        </q-item-section>
        
        <q-item-section>
          <q-item-label>{{ category.name }}</q-item-label>
          <q-item-label caption>
            {{ category.type }} • {{ category.description || 'No description' }}
          </q-item-label>
        </q-item-section>
        
        <q-item-section side v-if="showActions">
          <div class="row">
            <q-btn
              flat
              round
              dense
              color="primary"
              icon="edit"
              @click.stop="$emit('edit', category.id)"
            />
            <q-btn
              flat
              round
              dense
              color="negative"
              icon="delete"
              @click.stop="$emit('delete', category.id)"
            />
          </div>
        </q-item-section>
      </q-item>
    </q-list>
    
    <div v-if="categories.length === 0" class="text-center q-pa-md">
      <q-icon name="category" size="3rem" color="grey-5" />
      <div class="text-subtitle1 q-mt-sm">No categories found</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import { Category } from 'src/services/category.service';

const props = defineProps({
  categories: {
    type: Array as () => Category[],
    required: true
  },
  showActions: {
    type: Boolean,
    default: true
  }
});

defineEmits(['select', 'edit', 'delete']);
</script>
