<template>
  <q-page padding>
    <page-header title="Profile" show-back-button />
    
    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading" message="Loading profile data..." />
    
    <div class="row q-col-gutter-md">
      <!-- Profile Information -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6">Profile Information</div>
          </q-card-section>
          
          <q-card-section>
            <base-form
              ref="profileFormRef"
              :loading="savingProfile"
              submit-label="Save Changes"
              @submit="saveProfile"
            >
              <q-input
                v-model="profile.username"
                label="Username"
                :rules="[val => !!val || 'Username is required']"
                lazy-rules
              >
                <template v-slot:prepend>
                  <q-icon name="person" />
                </template>
              </q-input>
              
              <q-input
                v-model="profile.email"
                label="Email"
                type="email"
                :rules="[
                  val => !!val || 'Email is required',
                  val => /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(val) || 'Please enter a valid email'
                ]"
                lazy-rules
              >
                <template v-slot:prepend>
                  <q-icon name="email" />
                </template>
              </q-input>
              
              <q-input
                v-model="profile.fullName"
                label="Full Name"
              >
                <template v-slot:prepend>
                  <q-icon name="badge" />
                </template>
              </q-input>
              
              <q-input
                v-model="profile.phone"
                label="Phone Number"
              >
                <template v-slot:prepend>
                  <q-icon name="phone" />
                </template>
              </q-input>
            </base-form>
          </q-card-section>
        </q-card>
      </div>
      
      <!-- Change Password -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6">Change Password</div>
          </q-card-section>
          
          <q-card-section>
            <base-form
              ref="passwordFormRef"
              :loading="savingPassword"
              submit-label="Update Password"
              @submit="changePassword"
            >
              <q-input
                v-model="passwordForm.currentPassword"
                label="Current Password"
                :type="showPassword ? 'text' : 'password'"
                :rules="[val => !!val || 'Current password is required']"
                lazy-rules
              >
                <template v-slot:prepend>
                  <q-icon name="lock" />
                </template>
                <template v-slot:append>
                  <q-icon
                    :name="showPassword ? 'visibility_off' : 'visibility'"
                    class="cursor-pointer"
                    @click="showPassword = !showPassword"
                  />
                </template>
              </q-input>
              
              <q-input
                v-model="passwordForm.newPassword"
                label="New Password"
                :type="showPassword ? 'text' : 'password'"
                :rules="[
                  val => !!val || 'New password is required',
                  val => val.length >= 6 || 'Password must be at least 6 characters'
                ]"
                lazy-rules
              >
                <template v-slot:prepend>
                  <q-icon name="lock" />
                </template>
              </q-input>
              
              <q-input
                v-model="passwordForm.confirmPassword"
                label="Confirm New Password"
                :type="showPassword ? 'text' : 'password'"
                :rules="[
                  val => !!val || 'Please confirm your password',
                  val => val === passwordForm.newPassword || 'Passwords do not match'
                ]"
                lazy-rules
              >
                <template v-slot:prepend>
                  <q-icon name="lock" />
                </template>
              </q-input>
            </base-form>
          </q-card-section>
        </q-card>
      </div>
      
      <!-- Profile Picture -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">Profile Picture</div>
          </q-card-section>
          
          <q-card-section class="row items-center">
            <div class="col-12 col-md-4 flex justify-center">
              <q-avatar size="150px" font-size="52px" color="primary" text-color="white">
                {{ profile.username ? profile.username.charAt(0).toUpperCase() : 'U' }}
              </q-avatar>
            </div>
            
            <div class="col-12 col-md-8 q-mt-md q-mt-md-none">
              <p class="text-body1">Upload a new profile picture</p>
              <q-file
                v-model="profilePicture"
                label="Choose a file"
                accept="image/*"
                outlined
              >
                <template v-slot:prepend>
                  <q-icon name="attach_file" />
                </template>
              </q-file>
              
              <div class="q-mt-md">
                <q-btn
                  color="primary"
                  label="Upload"
                  :disable="!profilePicture"
                  :loading="uploadingPicture"
                  @click="uploadProfilePicture"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import BaseForm from 'src/components/common/BaseForm.vue';
import { useAuthStore } from 'src/stores/auth.store';
import NotificationService from 'src/services/notification.service';

const authStore = useAuthStore();

const loading = ref(false);
const savingProfile = ref(false);
const savingPassword = ref(false);
const uploadingPicture = ref(false);
const error = ref<string | null>(null);
const showPassword = ref(false);
const profilePicture = ref<File | null>(null);

const profileFormRef = ref<InstanceType<typeof BaseForm> | null>(null);
const passwordFormRef = ref<InstanceType<typeof BaseForm> | null>(null);

// Profile data
const profile = ref({
  username: 'johndoe',
  email: '<EMAIL>',
  fullName: 'John Doe',
  phone: '+****************'
});

// Password form
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// Save profile
const saveProfile = async () => {
  savingProfile.value = true;
  
  try {
    // In a real app, you would call an API to update the profile
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    NotificationService.success('Profile updated successfully');
  } catch (err: any) {
    error.value = 'Failed to update profile. Please try again.';
    NotificationService.error('Failed to update profile');
  } finally {
    savingProfile.value = false;
  }
};

// Change password
const changePassword = async () => {
  savingPassword.value = true;
  
  try {
    // In a real app, you would call an API to change the password
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    NotificationService.success('Password changed successfully');
    passwordForm.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    };
  } catch (err: any) {
    error.value = 'Failed to change password. Please try again.';
    NotificationService.error('Failed to change password');
  } finally {
    savingPassword.value = false;
  }
};

// Upload profile picture
const uploadProfilePicture = async () => {
  if (!profilePicture.value) return;
  
  uploadingPicture.value = true;
  
  try {
    // In a real app, you would call an API to upload the profile picture
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    NotificationService.success('Profile picture uploaded successfully');
    profilePicture.value = null;
  } catch (err: any) {
    error.value = 'Failed to upload profile picture. Please try again.';
    NotificationService.error('Failed to upload profile picture');
  } finally {
    uploadingPicture.value = false;
  }
};

// Load profile data
const loadProfile = async () => {
  loading.value = true;
  
  try {
    // In a real app, you would call an API to get the profile data
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // For demo purposes, we're using static data
    // profile.value = response.data;
  } catch (err: any) {
    error.value = 'Failed to load profile data. Please try again.';
    NotificationService.error('Failed to load profile data');
  } finally {
    loading.value = false;
  }
};

// Load profile data on component mount
loadProfile();
</script>
