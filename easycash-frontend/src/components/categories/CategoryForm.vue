<template>
  <base-form
    ref="formRef"
    :loading="loading"
    :submit-label="submitLabel"
    @submit="onSubmit"
    @reset="onReset"
  >
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <q-input
          v-model="form.name"
          label="Category Name"
          :rules="[val => !!val || 'Category name is required']"
          lazy-rules
        >
          <template v-slot:prepend>
            <q-icon name="label" />
          </template>
        </q-input>
      </div>
      
      <div class="col-12">
        <q-option-group
          v-model="form.type"
          :options="typeOptions"
          color="primary"
          inline
          dense
        />
      </div>
      
      <div class="col-12">
        <q-input
          v-model="form.description"
          label="Description"
          type="textarea"
          autogrow
        >
          <template v-slot:prepend>
            <q-icon name="description" />
          </template>
        </q-input>
      </div>
      
      <div class="col-12 col-md-6">
        <q-select
          v-model="form.icon"
          :options="icons"
          label="Icon"
          emit-value
          map-options
          option-label="label"
          option-value="value"
        >
          <template v-slot:option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section avatar>
                <q-icon :name="scope.opt.value" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.label }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
          
          <template v-slot:selected>
            <q-icon :name="form.icon || 'category'" class="q-mr-sm" />
            {{ getIconLabel(form.icon) }}
          </template>
        </q-select>
      </div>
      
      <div class="col-12 col-md-6">
        <q-select
          v-model="form.color"
          :options="colors"
          label="Color"
          emit-value
          map-options
          option-label="label"
          option-value="value"
        >
          <template v-slot:option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section avatar>
                <q-badge :style="`background-color: ${scope.opt.value}`" style="width: 25px; height: 25px" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ scope.opt.label }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
          
          <template v-slot:selected>
            <q-badge :style="`background-color: ${form.color}`" style="width: 25px; height: 25px" class="q-mr-sm" />
            {{ getColorLabel(form.color) }}
          </template>
        </q-select>
      </div>
      
      <div class="col-12">
        <q-toggle
          v-model="form.is_active"
          label="Active"
        />
      </div>
    </div>
  </base-form>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, computed } from 'vue';
import BaseForm from 'src/components/common/BaseForm.vue';
import { Category, CategoryRequest } from 'src/services/category.service';

const props = defineProps({
  category: {
    type: Object as () => Category | null,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'create',
    validator: (value: string) => ['create', 'edit'].includes(value)
  }
});

const emit = defineEmits(['submit', 'reset']);

const formRef = ref<InstanceType<typeof BaseForm> | null>(null);

const form = ref<CategoryRequest>({
  name: '',
  type: 'expense',
  description: '',
  icon: 'category',
  color: '#1976D2',
  is_active: true
});

// Type options
const typeOptions = [
  { label: 'Expense', value: 'expense' },
  { label: 'Income', value: 'income' }
];

// Icons
const icons = [
  { label: 'Category', value: 'category' },
  { label: 'Shopping', value: 'shopping_cart' },
  { label: 'Food', value: 'restaurant' },
  { label: 'Transport', value: 'directions_car' },
  { label: 'Home', value: 'home' },
  { label: 'Entertainment', value: 'movie' },
  { label: 'Health', value: 'local_hospital' },
  { label: 'Education', value: 'school' },
  { label: 'Travel', value: 'flight' },
  { label: 'Bills', value: 'receipt' },
  { label: 'Salary', value: 'work' },
  { label: 'Gift', value: 'card_giftcard' },
  { label: 'Other', value: 'more_horiz' }
];

// Colors
const colors = [
  { label: 'Blue', value: '#1976D2' },
  { label: 'Red', value: '#C62828' },
  { label: 'Green', value: '#2E7D32' },
  { label: 'Purple', value: '#7B1FA2' },
  { label: 'Orange', value: '#EF6C00' },
  { label: 'Teal', value: '#00796B' },
  { label: 'Pink', value: '#C2185B' },
  { label: 'Grey', value: '#616161' }
];

// Get icon label
const getIconLabel = (value: string | undefined) => {
  if (!value) return 'Category';
  const icon = icons.find(i => i.value === value);
  return icon ? icon.label : 'Category';
};

// Get color label
const getColorLabel = (value: string | undefined) => {
  if (!value) return 'Blue';
  const color = colors.find(c => c.value === value);
  return color ? color.label : 'Blue';
};

// Submit label
const submitLabel = computed(() => {
  return props.mode === 'create' ? 'Create Category' : 'Update Category';
});

// Initialize form with category data if in edit mode
watch(() => props.category, (newCategory) => {
  if (newCategory) {
    form.value = {
      name: newCategory.name,
      type: newCategory.type,
      description: newCategory.description || '',
      icon: newCategory.icon || 'category',
      color: newCategory.color || '#1976D2',
      is_active: newCategory.is_active
    };
  }
}, { immediate: true });

// Submit form
const onSubmit = () => {
  emit('submit', { ...form.value });
};

// Reset form
const onReset = () => {
  if (props.category) {
    form.value = {
      name: props.category.name,
      type: props.category.type,
      description: props.category.description || '',
      icon: props.category.icon || 'category',
      color: props.category.color || '#1976D2',
      is_active: props.category.is_active
    };
  } else {
    form.value = {
      name: '',
      type: 'expense',
      description: '',
      icon: 'category',
      color: '#1976D2',
      is_active: true
    };
  }
  
  emit('reset');
};

// Expose methods to parent component
defineExpose({
  validate: async () => await formRef.value?.validate(),
  reset: () => formRef.value?.reset()
});
</script>
