import ApiService from './api.service'
import { PaginatedResponse } from './account.service'
import { Account } from './account.service'
import { Category } from './category.service'

export interface Transaction {
  id: number
  user_id: number
  account_id: number
  category_id: number
  amount: number
  type: string
  date: string
  description?: string
  notes?: string
  location?: string
  tags?: string
  image_url?: string
  created_at: string
  updated_at: string
  account?: Account
  category?: Category
}

export interface TransactionRequest {
  account_id: number
  category_id: number
  amount: number
  type: string
  date: string
  description?: string
  notes?: string
  location?: string
  tags?: string
  image_url?: string
}

export interface TransactionListParams {
  account_id?: number
  category_id?: number
  type?: string
  start_date?: string
  end_date?: string
  min_amount?: number
  max_amount?: number
  search?: string
  page?: number
  limit?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export const TransactionService = {
  // Get all transactions
  getTransactions: async (params?: TransactionListParams): Promise<PaginatedResponse<Transaction>> => {
    try {
      return await ApiService.get<PaginatedResponse<Transaction>>('/transactions', { params })
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Get transaction by ID
  getTransaction: async (id: number): Promise<Transaction> => {
    try {
      return await ApiService.get<Transaction>(`/transactions/${id}`)
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Create new transaction
  createTransaction: async (transaction: TransactionRequest): Promise<Transaction> => {
    try {
      return await ApiService.post<Transaction>('/transactions', transaction)
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Update transaction
  updateTransaction: async (id: number, transaction: TransactionRequest): Promise<Transaction> => {
    try {
      return await ApiService.put<Transaction>(`/transactions/${id}`, transaction)
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Delete transaction
  deleteTransaction: async (id: number): Promise<void> => {
    try {
      await ApiService.delete<void>(`/transactions/${id}`)
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

export default TransactionService
