<template>
  <q-page padding>
    <page-header
      title="Account Details"
      show-back-button
    >
      <template v-slot:actions>
        <q-btn
          color="primary"
          icon="edit"
          label="Edit"
          :to="`/accounts/${accountId}/edit`"
          class="q-mr-sm"
        />
        <q-btn
          color="negative"
          icon="delete"
          label="Delete"
          @click="confirmDelete"
        />
      </template>
    </page-header>

    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading" message="Loading account data..." />

    <div v-if="account" class="row q-col-gutter-md">
      <!-- Account Summary -->
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section class="bg-primary text-white">
            <div class="row items-center no-wrap">
              <q-avatar :color="account.color || 'white'" text-color="primary" size="3rem">
                <q-icon :name="account.icon || 'account_balance'" size="2rem" />
              </q-avatar>
              <div class="q-ml-md">
                <div class="text-h6">{{ account.name }}</div>
                <div class="text-subtitle2">{{ account.type }}</div>
              </div>
            </div>
          </q-card-section>

          <q-card-section>
            <div class="text-h5 q-mb-md">
              {{ formatCurrency(account.balance, account.currency) }}
            </div>

            <q-list>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Currency</q-item-label>
                  <q-item-label>{{ account.currency }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section>
                  <q-item-label caption>Default Account</q-item-label>
                  <q-item-label>{{ account.is_default ? 'Yes' : 'No' }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section>
                  <q-item-label caption>Created</q-item-label>
                  <q-item-label>{{ formatDate(account.created_at) }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section>
                  <q-item-label caption>Last Updated</q-item-label>
                  <q-item-label>{{ formatDate(account.updated_at) }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </q-card>
      </div>

      <!-- Recent Transactions -->
      <div class="col-12 col-md-8">
        <q-card>
          <q-card-section class="q-pb-none">
            <div class="text-h6">Recent Transactions</div>
          </q-card-section>

          <q-card-section>
            <q-list v-if="accountTransactions.length > 0" separator>
              <q-item v-for="transaction in accountTransactions" :key="transaction.id" clickable @click="viewTransaction(transaction.id)">
                <q-item-section avatar>
                  <q-avatar :color="getTransactionColor(transaction.type)" text-color="white">
                    <q-icon :name="getTransactionIcon(transaction.type)" />
                  </q-avatar>
                </q-item-section>

                <q-item-section>
                  <q-item-label>{{ transaction.description || 'No description' }}</q-item-label>
                  <q-item-label caption>
                    {{ transaction.category || 'Uncategorized' }} • {{ formatDate(transaction.date) }}
                  </q-item-label>
                </q-item-section>

                <q-item-section side>
                  <q-item-label :class="getAmountClass(transaction.type)">
                    {{ getAmountPrefix(transaction.type) }}{{ formatCurrency(transaction.amount) }}
                  </q-item-label>
                </q-item-section>
              </q-item>
            </q-list>

            <div v-else class="text-center q-pa-md">
              <q-icon name="receipt_long" size="3rem" color="grey-5" />
              <div class="text-subtitle1 q-mt-sm">No transactions for this account</div>
              <q-btn color="primary" label="Add Transaction" class="q-mt-sm" :to="`/transactions/new?account_id=${accountId}`" />
            </div>
          </q-card-section>

          <q-card-actions align="right">
            <q-btn flat color="primary" label="View All" :to="`/transactions?account_id=${accountId}`" />
          </q-card-actions>
        </q-card>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <confirm-dialog
      v-model="showDeleteDialog"
      title="Delete Account"
      message="Are you sure you want to delete this account? This action cannot be undone."
      confirm-label="Delete"
      confirm-color="negative"
      :loading="deleteLoading"
      @confirm="deleteAccount"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { date } from 'quasar';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import ConfirmDialog from 'src/components/common/ConfirmDialog.vue';
import { useAccountStore } from 'src/stores/account.store';
import { useTransactionStore } from 'src/stores/transaction.store';
import { Transaction } from 'src/services/transaction.service';
import NotificationService from 'src/services/notification.service';

const props = defineProps({
  id: {
    type: Number,
    default: 0
  }
});

const router = useRouter();
const route = useRoute();
const accountStore = useAccountStore();
const transactionStore = useTransactionStore();

const loading = ref(false);
const error = ref<string | null>(null);
const showDeleteDialog = ref(false);
const deleteLoading = ref(false);

// Computed properties
const accountId = computed(() => props.id || Number(route.params.id));
const account = computed(() => accountStore.currentAccount);
const accountTransactions = computed(() => {
  return transactionStore.getTransactionsByAccount(accountId.value).slice(0, 5);
});

// Format currency
const formatCurrency = (value: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(value);
};

// Format date
const formatDate = (dateString: string) => {
  return date.formatDate(dateString, 'MMM D, YYYY');
};

// Get transaction icon
const getTransactionIcon = (type: string) => {
  switch (type) {
    case 'income':
      return 'arrow_upward';
    case 'expense':
      return 'arrow_downward';
    case 'transfer':
      return 'swap_horiz';
    default:
      return 'receipt_long';
  }
};

// Get transaction color
const getTransactionColor = (type: string) => {
  switch (type) {
    case 'income':
      return 'positive';
    case 'expense':
      return 'negative';
    case 'transfer':
      return 'info';
    default:
      return 'grey';
  }
};

// Get amount class
const getAmountClass = (type: string) => {
  switch (type) {
    case 'income':
      return 'text-positive';
    case 'expense':
      return 'text-negative';
    default:
      return '';
  }
};

// Get amount prefix
const getAmountPrefix = (type: string) => {
  switch (type) {
    case 'income':
      return '+';
    case 'expense':
      return '-';
    default:
      return '';
  }
};

// View transaction
const viewTransaction = (id: number) => {
  router.push(`/transactions/${id}`);
};

// Confirm delete
const confirmDelete = () => {
  showDeleteDialog.value = true;
};

// Delete account
const deleteAccount = async () => {
  deleteLoading.value = true;

  try {
    await accountStore.deleteAccount(accountId.value);
    NotificationService.success('Account deleted successfully');
    router.push('/accounts');
  } catch (err: any) {
    error.value = 'Failed to delete account. Please try again.';
    NotificationService.error('Failed to delete account');
    console.error('Delete account error:', err);
  } finally {
    deleteLoading.value = false;
    showDeleteDialog.value = false;
  }
};

// Load account data
const loadData = async () => {
  loading.value = true;
  error.value = null;

  try {
    // Load account and transactions in parallel
    await Promise.all([
      accountStore.fetchAccount(accountId.value),
      transactionStore.list({ account_id: accountId.value, limit: 5 })
    ]);
  } catch (err: any) {
    error.value = 'Failed to load account data. Please try again.';
    NotificationService.error('Failed to load account data');
    console.error('Account detail error:', err);
  } finally {
    loading.value = false;
  }
};

// Load data on component mount
onMounted(() => {
  loadData();
});
</script>
