<template>
  <q-item clickable @click="$emit('click')">
    <q-item-section avatar>
      <q-avatar :color="transactionColor" text-color="white">
        <q-icon :name="transactionIcon" />
      </q-avatar>
    </q-item-section>

    <q-item-section>
      <q-item-label>{{ transaction.description || 'No description' }}</q-item-label>
      <q-item-label caption>
        <span>{{ transaction.category || 'Uncategorized' }}</span>
        <span v-if="transaction.account" class="q-mx-xs">•</span>
        <span v-if="transaction.account">{{ transaction.account }}</span>
      </q-item-label>
    </q-item-section>

    <q-item-section side>
      <q-item-label :class="amountClass">
        {{ amountPrefix }}{{ formatCurrency(transaction.amount) }}
      </q-item-label>
      <q-item-label caption>{{ formatTime(transaction.date) }}</q-item-label>
    </q-item-section>

    <q-item-section side v-if="showActions">
      <div class="row">
        <q-btn
          flat
          round
          dense
          color="primary"
          icon="edit"
          @click.stop="$emit('edit')"
        />
        <q-btn
          flat
          round
          dense
          color="negative"
          icon="delete"
          @click.stop="$emit('delete')"
        />
      </div>
    </q-item-section>
  </q-item>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';
import { date } from 'quasar';
import type { Transaction } from '../../types/models/transaction';

const props = defineProps({
  transaction: {
    type: Object as () => Transaction,
    required: true
  },
  showActions: {
    type: Boolean,
    default: true
  }
});

defineEmits(['click', 'edit', 'delete']);

// Transaction icon
const transactionIcon = computed(() => {
  switch (props.transaction.type) {
    case 'income':
      return 'arrow_upward';
    case 'expense':
      return 'arrow_downward';
    case 'transfer':
      return 'swap_horiz';
    default:
      return 'receipt_long';
  }
});

// Transaction color
const transactionColor = computed(() => {
  switch (props.transaction.type) {
    case 'income':
      return 'positive';
    case 'expense':
      return 'negative';
    case 'transfer':
      return 'info';
    default:
      return 'grey';
  }
});

// Amount class
const amountClass = computed(() => {
  switch (props.transaction.type) {
    case 'income':
      return 'text-positive';
    case 'expense':
      return 'text-negative';
    default:
      return '';
  }
});

// Amount prefix
const amountPrefix = computed(() => {
  switch (props.transaction.type) {
    case 'income':
      return '+';
    case 'expense':
      return '-';
    default:
      return '';
  }
});

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Format time
const formatTime = (dateString: string) => {
  return date.formatDate(dateString, 'h:mm A');
};
</script>
<script lang="ts">
export default {
  name: 'TransactionListItem'
};
</script>
