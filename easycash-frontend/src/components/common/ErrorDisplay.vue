<template>
  <q-banner v-if="error" class="bg-negative text-white q-mb-md" rounded>
    <template v-slot:avatar>
      <q-icon name="error" />
    </template>
    <div class="text-body1">{{ errorMessage }}</div>
    <template v-slot:action>
      <q-btn flat color="white" label="Dismiss" @click="dismissError" />
    </template>
  </q-banner>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue';

const props = defineProps({
  error: {
    type: [String, Error, Object, null],
    default: null
  }
});

const emit = defineEmits(['dismiss']);

const errorMessage = computed(() => {
  if (!props.error) return '';
  
  if (typeof props.error === 'string') {
    return props.error;
  }
  
  if (props.error instanceof Error) {
    return props.error.message;
  }
  
  if (typeof props.error === 'object') {
    // Handle axios error or other error objects
    if ('response' in props.error && props.error.response?.data?.message) {
      return props.error.response.data.message;
    }
    
    // Try to stringify the error object
    try {
      return JSON.stringify(props.error);
    } catch (e) {
      return 'An unknown error occurred';
    }
  }
  
  return 'An unknown error occurred';
});

const dismissError = () => {
  emit('dismiss');
};
</script>
