<template>
  <base-form
    ref="formRef"
    :loading="loading"
    :submit-label="submitLabel"
    @submit="onSubmit"
    @reset="onReset"
  >
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <q-option-group
          v-model="form.type"
          :options="transactionTypes"
          color="primary"
          inline
          dense
        />
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model.number="form.amount"
          label="Amount"
          type="number"
          step="0.01"
          :rules="[val => val > 0 || 'Amount must be greater than 0']"
          lazy-rules
        >
          <template v-slot:prepend>
            <q-icon name="attach_money" />
          </template>
        </q-input>
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model="form.date"
          label="Date"
          :rules="[val => !!val || 'Date is required']"
          lazy-rules
        >
          <template v-slot:prepend>
            <q-icon name="event" />
          </template>
          <template v-slot:append>
            <q-icon name="event" class="cursor-pointer">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-date v-model="form.date" mask="YYYY-MM-DD" />
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>
      </div>

      <div class="col-12 col-md-6">
        <q-select
          v-model="form.account_id"
          :options="accountOptions"
          label="Account"
          :rules="[val => !!val || 'Account is required']"
          lazy-rules
          emit-value
          map-options
          option-label="label"
          option-value="value"
        >
          <template v-slot:prepend>
            <q-icon name="account_balance" />
          </template>
        </q-select>
      </div>

      <div class="col-12 col-md-6">
        <q-select
          v-model="form.category_id"
          :options="categoryOptions"
          label="Category"
          :rules="[val => !!val || 'Category is required']"
          lazy-rules
          emit-value
          map-options
          option-label="label"
          option-value="value"
        >
          <template v-slot:prepend>
            <q-icon name="category" />
          </template>
        </q-select>
      </div>

      <div class="col-12">
        <q-input
          v-model="form.description"
          label="Description"
          :rules="[val => !!val || 'Description is required']"
          lazy-rules
        >
          <template v-slot:prepend>
            <q-icon name="description" />
          </template>
        </q-input>
      </div>

      <div class="col-12">
        <q-input
          v-model="form.notes"
          label="Notes"
          type="textarea"
          autogrow
        >
          <template v-slot:prepend>
            <q-icon name="notes" />
          </template>
        </q-input>
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model="form.location"
          label="Location"
        >
          <template v-slot:prepend>
            <q-icon name="location_on" />
          </template>
        </q-input>
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model="form.tags"
          label="Tags"
          hint="Separate tags with commas"
        >
          <template v-slot:prepend>
            <q-icon name="local_offer" />
          </template>
        </q-input>
      </div>
    </div>
  </base-form>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, onMounted, computed } from 'vue';
import { date } from 'quasar';
import BaseForm from 'src/components/common/BaseForm.vue';
import type { Transaction } from '../../types/models/transaction';
import type { TransactionRequest } from 'src/services/transaction.service';
import { useAccountStore } from 'src/stores/account.store';
import { useCategoryStore } from 'src/stores/category.store';

const props = defineProps({
  transaction: {
    type: Object as () => Transaction | null,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'create',
    validator: (value: string) => ['create', 'edit'].includes(value)
  },
  initialAccountId: {
    type: Number,
    default: 0
  },
  initialCategoryId: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['submit', 'reset']);

const formRef = ref<InstanceType<typeof BaseForm> | null>(null);
const accountStore = useAccountStore();
const categoryStore = useCategoryStore();

// Transaction types
const transactionTypes = [
  { label: 'Expense', value: 'expense' },
  { label: 'Income', value: 'income' },
  { label: 'Transfer', value: 'transfer' }
];

// Form data
const form = ref<TransactionRequest>({
  account_id: props.initialAccountId || 0,
  category_id: props.initialCategoryId || 0,
  amount: 0,
  type: 'expense',
  date: date.formatDate(new Date(), 'YYYY-MM-DD'),
  description: '',
  notes: '',
  location: '',
  tags: '',
  image_url: ''
});

// Account options
const accountOptions = computed(() => {
  return accountStore.accounts.map(account => ({
    label: account.name,
    value: account.id
  }));
});

// Category options
const categoryOptions = computed(() => {
  // Filter categories based on transaction type
  const categories = form.value.type === 'income'
    ? categoryStore.incomeCategories
    : categoryStore.expenseCategories;

  return categories.map(category => ({
    label: category.name,
    value: category.id
  }));
});

// Submit label
const submitLabel = computed(() => {
  return props.mode === 'create' ? 'Create Transaction' : 'Update Transaction';
});

// Initialize form with transaction data if in edit mode
watch(() => props.transaction, (newTransaction) => {
  if (newTransaction) {
    form.value = {
      account_id: newTransaction.accountId,
      category_id: newTransaction.categoryId,
      amount: newTransaction.amount,
      type: newTransaction.type,
      date: newTransaction.date.substring(0, 10), // Extract YYYY-MM-DD
      description: newTransaction.description || '',
      notes: newTransaction.notes || '',
      location: newTransaction.location || '',
      tags: newTransaction.tags || '',
      image_url: newTransaction.imageUrl || ''
    };
  }
}, { immediate: true });

// Watch transaction type to update category options
watch(() => form.value.type, (newType) => {
  // Reset category when type changes
  form.value.category_id = 0;
});

// Submit form
const onSubmit = () => {
  emit('submit', { ...form.value });
};

// Reset form
const onReset = () => {
  if (props.transaction) {
    form.value = {
      account_id: props.transaction.accountId,
      category_id: props.transaction.categoryId,
      amount: props.transaction.amount,
      type: props.transaction.type,
      date: props.transaction.date.substring(0, 10),
      description: props.transaction.description || '',
      notes: props.transaction.notes || '',
      location: props.transaction.location || '',
      tags: props.transaction.tags || '',
      image_url: props.transaction.imageUrl || ''
    };
  } else {
    form.value = {
      account_id: props.initialAccountId || 0,
      category_id: props.initialCategoryId || 0,
      amount: 0,
      type: 'expense',
      date: date.formatDate(new Date(), 'YYYY-MM-DD'),
      description: '',
      notes: '',
      location: '',
      tags: '',
      image_url: ''
    };
  }

  emit('reset');
};

// Load accounts and categories on component mount
onMounted(async () => {
  if (accountStore.accounts.length === 0) {
    await accountStore.fetchAccounts();

    // Set default account if available and no initial account provided
    if (!props.initialAccountId && !props.transaction && accountStore.defaultAccount) {
      form.value.account_id = accountStore.defaultAccount.id;
    }
  }

  if (categoryStore.categories.length === 0) {
    await categoryStore.fetchCategories();
  }
});

// Expose methods to parent component
defineExpose({
  validate: async () => await formRef.value?.validate(),
  reset: () => formRef.value?.reset()
});
</script>
