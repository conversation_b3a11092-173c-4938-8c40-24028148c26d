<template>
  <q-page padding>
    <page-header
      :title="mode === 'create' ? 'Create Budget' : 'Edit Budget'"
      show-back-button
    />
    
    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading && !formLoading" message="Loading budget data..." />
    
    <q-card>
      <q-card-section>
        <budget-form
          ref="formRef"
          :budget="budget"
          :loading="formLoading"
          :mode="mode"
          @submit="onSubmit"
          @reset="onReset"
        />
      </q-card-section>
    </q-card>
    
    <q-card v-if="isEditMode && budget" class="q-mt-md">
      <q-card-section class="q-pb-none">
        <div class="text-h6">Budget Progress</div>
      </q-card-section>
      
      <q-card-section>
        <div class="row items-center justify-between q-mb-xs">
          <div class="col">{{ budget.category?.name || 'Uncategorized' }}</div>
          <div class="col-auto">
            {{ formatCurrency(budget.spent_amount || 0) }} / {{ formatCurrency(budget.amount) }}
          </div>
        </div>
        <q-linear-progress
          :value="getBudgetProgress(budget)"
          :color="getBudgetColor(budget)"
          size="md"
        />
        <div class="row justify-between q-mt-xs">
          <div class="col-auto text-caption">
            {{ formatDateRange(budget.start_date, budget.end_date) }}
          </div>
          <div class="col-auto text-caption">
            {{ Math.round(budget.percentage || 0) }}% used
          </div>
        </div>
      </q-card-section>
      
      <q-card-section v-if="budget.transactions && budget.transactions.length > 0">
        <div class="text-subtitle1 q-mb-sm">Recent Transactions</div>
        <q-list separator>
          <q-item v-for="transaction in budget.transactions.slice(0, 5)" :key="transaction.id" clickable @click="viewTransaction(transaction.id)">
            <q-item-section avatar>
              <q-avatar color="negative" text-color="white">
                <q-icon name="arrow_downward" />
              </q-avatar>
            </q-item-section>
            
            <q-item-section>
              <q-item-label>{{ transaction.description || 'No description' }}</q-item-label>
              <q-item-label caption>{{ formatDate(transaction.date) }}</q-item-label>
            </q-item-section>
            
            <q-item-section side>
              <q-item-label class="text-negative">
                -{{ formatCurrency(transaction.amount) }}
              </q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { date } from 'quasar';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import BudgetForm from 'src/components/budgets/BudgetForm.vue';
import { useBudgetStore } from 'src/stores/budget.store';
import { BudgetRequest } from 'src/services/budget.service';
import NotificationService from 'src/services/notification.service';

const props = defineProps({
  id: {
    type: Number,
    default: 0
  },
  mode: {
    type: String,
    default: 'create',
    validator: (value: string) => ['create', 'edit'].includes(value)
  }
});

const router = useRouter();
const route = useRoute();
const budgetStore = useBudgetStore();

const formRef = ref<InstanceType<typeof BudgetForm> | null>(null);
const loading = ref(false);
const formLoading = ref(false);
const error = ref<string | null>(null);
const budget = ref(budgetStore.currentBudget);

// Computed properties
const isEditMode = computed(() => props.mode === 'edit');
const budgetId = computed(() => props.id || Number(route.params.id));

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Format date
const formatDate = (dateString: string) => {
  return date.formatDate(dateString, 'MMM D, YYYY');
};

// Format date range
const formatDateRange = (startDate: string, endDate: string) => {
  const start = date.formatDate(startDate, 'MMM D, YYYY');
  const end = date.formatDate(endDate, 'MMM D, YYYY');
  return `${start} - ${end}`;
};

// Get budget progress
const getBudgetProgress = (budget: any) => {
  if (budget.amount <= 0) return 0;
  const progress = (budget.spent_amount || 0) / budget.amount;
  return Math.min(progress, 1); // Cap at 100%
};

// Get budget color
const getBudgetColor = (budget: any) => {
  const progress = getBudgetProgress(budget);
  if (progress >= 0.9) return 'negative';
  if (progress >= 0.7) return 'warning';
  return 'positive';
};

// View transaction
const viewTransaction = (id: number) => {
  router.push(`/transactions/${id}`);
};

// Load budget data if in edit mode
const loadBudget = async () => {
  if (!isEditMode.value || !budgetId.value) return;
  
  loading.value = true;
  error.value = null;
  
  try {
    await budgetStore.fetchBudget(budgetId.value);
    budget.value = budgetStore.currentBudget;
  } catch (err: any) {
    error.value = 'Failed to load budget. Please try again.';
    NotificationService.error('Failed to load budget');
    console.error('Load budget error:', err);
  } finally {
    loading.value = false;
  }
};

// Submit form
const onSubmit = async (formData: BudgetRequest) => {
  formLoading.value = true;
  error.value = null;
  
  try {
    if (isEditMode.value && budgetId.value) {
      await budgetStore.updateBudget(budgetId.value, formData);
      NotificationService.success('Budget updated successfully');
    } else {
      await budgetStore.createBudget(formData);
      NotificationService.success('Budget created successfully');
    }
    
    router.push('/budgets');
  } catch (err: any) {
    error.value = isEditMode.value
      ? 'Failed to update budget. Please try again.'
      : 'Failed to create budget. Please try again.';
    NotificationService.error(error.value);
    console.error('Budget form error:', err);
  } finally {
    formLoading.value = false;
  }
};

// Reset form
const onReset = () => {
  error.value = null;
};

// Load budget data on component mount
onMounted(() => {
  loadBudget();
});
</script>
