package controller

import (
	"github.com/gin-gonic/gin"

	"github.com/imaojia/easycash/internal/middleware"
	"github.com/imaojia/easycash/internal/model"
	"github.com/imaojia/easycash/internal/pkg/response"
	"github.com/imaojia/easycash/internal/service"
)

// AuthController handles authentication related requests
type AuthController struct {
	authService *service.AuthService
}

// NewAuthController creates a new auth controller
func NewAuthController(authService *service.AuthService) *AuthController {
	return &AuthController{authService: authService}
}

// Register handles user registration
// @Summary Register a new user
// @Description Register a new user with username, email and password
// @Tags auth
// @Accept json
// @Produce json
// @Param request body model.RegisterRequest true "Registration details"
// @Success 200 {object} response.Response{data=model.UserResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/auth/register [post]
func (c *AuthController) Register(ctx *gin.Context) {
	var req model.RegisterRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	user, err := c.authService.Register(req)
	if err != nil {
		response.Error(ctx, 400, err.Error())
		return
	}

	response.Success(ctx, user)
}

// Login handles user login
// @Summary Login a user
// @Description Login with email and password
// @Tags auth
// @Accept json
// @Produce json
// @Param request body model.LoginRequest true "Login details"
// @Success 200 {object} response.Response{data=model.UserResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/auth/login [post]
func (c *AuthController) Login(ctx *gin.Context) {
	var req model.LoginRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	user, err := c.authService.Login(req)
	if err != nil {
		response.Error(ctx, 400, err.Error())
		return
	}

	response.Success(ctx, user)
}

// GetProfile retrieves the current user's profile
// @Summary Get user profile
// @Description Get the current user's profile
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=model.UserResponse}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/auth/profile [get]
func (c *AuthController) GetProfile(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)
	
	user, err := c.authService.GetUserByID(userID)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}
	
	response.Success(ctx, model.UserResponse{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Avatar:   user.Avatar,
	})
}

// UpdateProfile updates the current user's profile
// @Summary Update user profile
// @Description Update the current user's profile
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.UpdateProfileRequest true "Profile details"
// @Success 200 {object} response.Response{data=model.UserResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/auth/profile [put]
func (c *AuthController) UpdateProfile(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)
	
	var req struct {
		Username string `json:"username" binding:"required,min=3,max=50"`
		Email    string `json:"email" binding:"required,email"`
		Avatar   string `json:"avatar,omitempty"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}
	
	user, err := c.authService.UpdateUser(userID, req.Username, req.Email, req.Avatar)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}
	
	response.Success(ctx, user)
}

// ChangePassword changes the current user's password
// @Summary Change password
// @Description Change the current user's password
// @Tags auth
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.ChangePasswordRequest true "Password details"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/auth/change-password [post]
func (c *AuthController) ChangePassword(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)
	
	var req struct {
		OldPassword string `json:"old_password" binding:"required,min=6"`
		NewPassword string `json:"new_password" binding:"required,min=6"`
	}
	
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}
	
	if err := c.authService.ChangePassword(userID, req.OldPassword, req.NewPassword); err != nil {
		response.Error(ctx, 400, err.Error())
		return
	}
	
	response.Success(ctx, gin.H{"message": "Password changed successfully"})
}
