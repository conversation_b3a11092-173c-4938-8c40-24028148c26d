<template>
  <q-page class="flex flex-center">
    <q-card class="register-card">
      <q-card-section class="text-center">
        <div class="text-h5 q-mb-md">注册 EasyCash</div>
        <div class="text-subtitle2 text-grey">创建您的账号，开始管理财务</div>
      </q-card-section>

      <q-card-section>
        <q-form @submit="handleSubmit" class="q-gutter-md">
          <q-input
            v-model="form.username"
            label="用户名"
            :rules="[
              val => !!val || '请输入用户名',
              val => val.length >= 3 || '用户名至少3个字符'
            ]"
          />

          <q-input
            v-model="form.email"
            type="email"
            label="邮箱"
            :rules="[
              val => !!val || '请输入邮箱',
              val => validateEmail(val) || '请输入有效的邮箱'
            ]"
          />

          <q-input
            v-model="form.password"
            :type="showPassword ? 'text' : 'password'"
            label="密码"
            :rules="[
              val => !!val || '请输入密码',
              val => val.length >= 6 || '密码至少6个字符'
            ]"
          >
            <template v-slot:append>
              <q-icon
                :name="showPassword ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="showPassword = !showPassword"
              />
            </template>
          </q-input>

          <q-input
            v-model="form.confirmPassword"
            :type="showConfirmPassword ? 'text' : 'password'"
            label="确认密码"
            :rules="[
              val => !!val || '请确认密码',
              val => val === form.password || '两次输入的密码不一致'
            ]"
          >
            <template v-slot:append>
              <q-icon
                :name="showConfirmPassword ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="showConfirmPassword = !showConfirmPassword"
              />
            </template>
          </q-input>

          <div class="q-mt-sm">
            <q-checkbox v-model="agreeTerms" label="我同意服务条款和隐私政策" />
          </div>

          <q-btn
            type="submit"
            color="primary"
            class="full-width"
            label="注册"
            :loading="loading"
            :disable="!agreeTerms"
          />
        </q-form>
      </q-card-section>

      <q-card-section class="text-center q-pa-none">
        <q-separator class="q-my-md" />
        <div class="text-grey q-pa-md">
          已有账号？
          <q-btn flat color="primary" label="立即登录" to="/auth/login" />
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useAuthStore } from 'src/stores/auth.store'

const router = useRouter()
const $q = useQuasar()
const authStore = useAuthStore()

const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const agreeTerms = ref(false)

const form = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const validateEmail = (val: string) => {
  const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/
  return emailPattern.test(val)
}

const handleSubmit = async () => {
  if (!agreeTerms.value) {
    $q.notify({
      type: 'warning',
      message: '请同意服务条款和隐私政策'
    })
    return
  }

  loading.value = true
  try {
    await authStore.register({
      username: form.username,
      email: form.email,
      password: form.password
    })
    $q.notify({
      type: 'positive',
      message: '注册成功'
    })
    void router.push('/dashboard')
  } catch (err: unknown) {
    console.error('Registration failed:', err)
    $q.notify({
      type: 'negative',
      message: err instanceof Error ? err.message : '注册失败'
    })
  } finally {
    loading.value = false
  }
}

defineOptions({
  name: 'UserRegisterPage'
})
</script>

<style lang="scss" scoped>
.register-card {
  width: 100%;
  max-width: 400px;
  padding: 20px;
}
</style> 