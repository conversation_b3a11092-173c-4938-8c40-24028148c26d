import ApiService from './api.service'

export interface Account {
  id: number
  user_id: number
  name: string
  type: string
  balance: number
  currency: string
  icon?: string
  color?: string
  is_default: boolean
  created_at: string
  updated_at: string
}

export interface AccountRequest {
  name: string
  type: string
  balance: number
  currency: string
  icon?: string
  color?: string
  is_default: boolean
}

export interface AccountListParams {
  type?: string
  page?: number
  limit?: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
}

export const AccountService = {
  // Get all accounts
  getAccounts: async (params?: AccountListParams): Promise<PaginatedResponse<Account>> => {
    try {
      return await ApiService.get<PaginatedResponse<Account>>('/accounts', { params })
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Get account by ID
  getAccount: async (id: number): Promise<Account> => {
    try {
      return await ApiService.get<Account>(`/accounts/${id}`)
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Get default account
  getDefaultAccount: async (): Promise<Account> => {
    try {
      return await ApiService.get<Account>('/accounts/default')
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Create new account
  createAccount: async (account: AccountRequest): Promise<Account> => {
    try {
      return await ApiService.post<Account>('/accounts', account)
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Update account
  updateAccount: async (id: number, account: AccountRequest): Promise<Account> => {
    try {
      return await ApiService.put<Account>(`/accounts/${id}`, account)
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Delete account
  deleteAccount: async (id: number): Promise<void> => {
    try {
      await ApiService.delete<void>(`/accounts/${id}`)
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

export default AccountService
