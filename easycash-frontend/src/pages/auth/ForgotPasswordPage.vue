<template>
  <q-page class="flex flex-center">
    <q-card class="forgot-password-card">
      <q-card-section class="text-center">
        <div class="text-h4 text-primary q-mb-md">EasyCash</div>
        <div class="text-h5 q-mb-md">Forgot Password</div>
        <div class="text-body1 q-mb-lg">
          Enter your email address and we'll send you a link to reset your password.
        </div>
      </q-card-section>

      <q-card-section>
        <error-display :error="error" @dismiss="error = null" />

        <div v-if="emailSent" class="text-positive q-pa-md text-center">
          <q-icon name="check_circle" size="2rem" />
          <div class="text-h6 q-mt-sm">Email Sent</div>
          <div class="q-mt-sm">
            We've sent a password reset link to your email address.
            Please check your inbox and follow the instructions.
          </div>
        </div>

        <base-form
          v-else
          ref="formRef"
          :loading="loading"
          submit-label="Send Reset Link"
          @submit="onSubmit"
        >
          <q-input
            v-model="email"
            label="Email"
            type="email"
            :rules="[
              val => !!val || 'Email is required',
              val => /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(val) || 'Please enter a valid email'
            ]"
            lazy-rules
          >
            <template v-slot:prepend>
              <q-icon name="email" />
            </template>
          </q-input>
        </base-form>
      </q-card-section>

      <q-card-section class="text-center">
        <div>
          <router-link to="/auth/login" class="text-primary">
            Back to Sign In
          </router-link>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import BaseForm from 'src/components/common/BaseForm.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import NotificationService from 'src/services/notification.service';

const formRef = ref<InstanceType<typeof BaseForm> | null>(null);
const loading = ref(false);
const error = ref<string | null>(null);
const email = ref('');
const emailSent = ref(false);

const onSubmit = async () => {
  try {
    loading.value = true;
    error.value = null;
    
    // In a real app, you would call an API to send a password reset email
    // For now, we'll just simulate a successful response
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    emailSent.value = true;
    NotificationService.success('Password reset email sent');
  } catch (err: any) {
    error.value = err.message || 'Failed to send password reset email. Please try again.';
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.forgot-password-card {
  width: 100%;
  max-width: 400px;
  padding: 20px;
}
</style>
