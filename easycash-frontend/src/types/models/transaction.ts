/**
 * Transaction model for UI components
 */
export interface Transaction {
  id: number;
  date: string;
  description: string;
  category: string;
  amount: number;
  type: 'income' | 'expense' | 'transfer';
  createdAt: string;
  updatedAt: string;
  // IDs for filtering operations
  accountId: number;
  categoryId: number;
  // Optional fields that might be used in some components
  account?: string;
  notes?: string;
  location?: string;
  tags?: string;
  imageUrl?: string;
}

/**
 * Parameters for listing transactions
 */
export interface TransactionListParams {
  page?: number;
  pageSize?: number;
  search?: string;
  startDate?: string;
  endDate?: string;
  category?: string;
  type?: 'income' | 'expense';
  accountId?: number;
  minAmount?: number;
  maxAmount?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Response format for transaction listings
 */
export interface TransactionListResponse {
  items: Transaction[];
  total: number;
  page: number;
  pageSize: number;
}