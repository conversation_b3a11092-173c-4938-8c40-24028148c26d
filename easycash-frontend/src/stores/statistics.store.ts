import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import StatisticsService, {
  DailyStatistics,
  MonthlyStatistics,
  YearlyStatistics,
  CategoryStatistics,
  AccountStatistics
} from 'src/services/statistics.service'

export const useStatisticsStore = defineStore('statistics', () => {
  // State
  const dailyStats = ref<DailyStatistics[]>([])
  const monthlyStats = ref<MonthlyStatistics[]>([])
  const yearlyStats = ref<YearlyStatistics[]>([])
  const categoryStats = ref<CategoryStatistics[]>([])
  const accountStats = ref<AccountStatistics[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // Getters
  const totalIncome = computed(() => {
    return dailyStats.value.reduce((sum, stat) => sum + stat.income, 0)
  })
  
  const totalExpense = computed(() => {
    return dailyStats.value.reduce((sum, stat) => sum + stat.expense, 0)
  })
  
  const totalBalance = computed(() => {
    return totalIncome.value - totalExpense.value
  })
  
  const monthlyIncome = computed(() => {
    return monthlyStats.value.reduce((sum, stat) => sum + stat.income, 0)
  })
  
  const monthlyExpense = computed(() => {
    return monthlyStats.value.reduce((sum, stat) => sum + stat.expense, 0)
  })
  
  const monthlyBalance = computed(() => {
    return monthlyIncome.value - monthlyExpense.value
  })
  
  const yearlyIncome = computed(() => {
    return yearlyStats.value.reduce((sum, stat) => sum + stat.income, 0)
  })
  
  const yearlyExpense = computed(() => {
    return yearlyStats.value.reduce((sum, stat) => sum + stat.expense, 0)
  })
  
  const yearlyBalance = computed(() => {
    return yearlyIncome.value - yearlyExpense.value
  })
  
  const totalAccountBalance = computed(() => {
    return accountStats.value.reduce((sum, stat) => sum + stat.balance, 0)
  })
  
  // Actions
  async function fetchDailyStatistics(startDate: string, endDate: string) {
    loading.value = true
    error.value = null
    
    try {
      const stats = await StatisticsService.getDailyStatistics(startDate, endDate)
      dailyStats.value = stats
      return stats
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch daily statistics'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function fetchMonthlyStatistics(startDate: string, endDate: string) {
    loading.value = true
    error.value = null
    
    try {
      const stats = await StatisticsService.getMonthlyStatistics(startDate, endDate)
      monthlyStats.value = stats
      return stats
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch monthly statistics'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function fetchYearlyStatistics(startYear: number, endYear: number) {
    loading.value = true
    error.value = null
    
    try {
      const stats = await StatisticsService.getYearlyStatistics(startYear, endYear)
      yearlyStats.value = stats
      return stats
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch yearly statistics'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function fetchCategoryStatistics(startDate: string, endDate: string, type?: string) {
    loading.value = true
    error.value = null
    
    try {
      const stats = await StatisticsService.getCategoryStatistics(startDate, endDate, type)
      categoryStats.value = stats
      return stats
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch category statistics'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  async function fetchAccountStatistics() {
    loading.value = true
    error.value = null
    
    try {
      const stats = await StatisticsService.getAccountStatistics()
      accountStats.value = stats
      return stats
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch account statistics'
      throw error.value
    } finally {
      loading.value = false
    }
  }
  
  return {
    // State
    dailyStats,
    monthlyStats,
    yearlyStats,
    categoryStats,
    accountStats,
    loading,
    error,
    
    // Getters
    totalIncome,
    totalExpense,
    totalBalance,
    monthlyIncome,
    monthlyExpense,
    monthlyBalance,
    yearlyIncome,
    yearlyExpense,
    yearlyBalance,
    totalAccountBalance,
    
    // Actions
    fetchDailyStatistics,
    fetchMonthlyStatistics,
    fetchYearlyStatistics,
    fetchCategoryStatistics,
    fetchAccountStatistics
  }
})

export default useStatisticsStore
