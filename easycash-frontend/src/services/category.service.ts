import ApiService from './api.service'
import { PaginatedResponse } from './account.service'

export interface Category {
  id: number
  user_id: number
  name: string
  type: string
  icon?: string
  color?: string
  parent_id?: number
  description?: string
  is_active?: boolean
  created_at: string
  updated_at: string
  children?: Category[]
}

export interface CategoryRequest {
  name: string
  type: string
  icon?: string
  color?: string
  parent_id?: number
  description?: string
  is_active?: boolean
}

export interface CategoryListParams {
  type?: string
  parent_id?: number
  page?: number
  limit?: number
  search?: string
}

export const CategoryService = {
  // Get all categories
  getCategories: async (params?: CategoryListParams): Promise<PaginatedResponse<Category>> => {
    try {
      return await ApiService.get<PaginatedResponse<Category>>('/categories', { params })
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // Get category by ID
  getCategory: async (id: number): Promise<Category> => {
    try {
      return await ApiService.get<Category>(`/categories/${id}`)
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // Get category with children
  getCategoryWithChildren: async (id: number): Promise<Category> => {
    try {
      return await ApiService.get<Category>(`/categories/${id}/with-children`)
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // Get category hierarchy
  getCategoryHierarchy: async (type?: string): Promise<Category[]> => {
    try {
      return await ApiService.get<Category[]>('/categories/hierarchy', { params: { type } })
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // Create new category
  createCategory: async (category: CategoryRequest): Promise<Category> => {
    try {
      return await ApiService.post<Category>('/categories', category)
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // Update category
  updateCategory: async (id: number, category: CategoryRequest): Promise<Category> => {
    try {
      return await ApiService.put<Category>(`/categories/${id}`, category)
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // Delete category
  deleteCategory: async (id: number): Promise<void> => {
    try {
      await ApiService.delete<void>(`/categories/${id}`)
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

export default CategoryService
