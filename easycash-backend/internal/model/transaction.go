package model

import (
	"time"

	"gorm.io/gorm"
)

// Transaction represents a financial transaction
type Transaction struct {
	ID          uint      `json:"id" gorm:"primarykey"`
	UserID      uint      `json:"user_id" gorm:"not null;index"`
	AccountID   uint      `json:"account_id" gorm:"not null;index"`
	CategoryID  uint      `json:"category_id" gorm:"index"`
	Amount      float64   `json:"amount" gorm:"type:decimal(10,2);not null"`
	Type        string    `json:"type" gorm:"size:20;not null"` // income, expense, transfer
	Date        time.Time `json:"date" gorm:"not null;index"`
	Description string    `json:"description,omitempty" gorm:"type:text"`
	Notes       string    `json:"notes,omitempty" gorm:"type:text"`
	Location    string    `json:"location,omitempty" gorm:"size:255"`
	Tags        string    `json:"tags,omitempty" gorm:"size:255"`
	ImageURL    string    `json:"image_url,omitempty" gorm:"size:255"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relations
	User     User     `json:"-" gorm:"foreignKey:UserID"`
	Account  Account  `json:"-" gorm:"foreignKey:AccountID"`
	Category Category `json:"-" gorm:"foreignKey:CategoryID"`
}

// TransactionRequest is used for creating or updating a transaction
type TransactionRequest struct {
	AccountID   uint      `json:"account_id" binding:"required"`
	CategoryID  uint      `json:"category_id" binding:"required"`
	Amount      float64   `json:"amount" binding:"required"`
	Type        string    `json:"type" binding:"required"`
	Date        time.Time `json:"date" binding:"required"`
	Description string    `json:"description,omitempty"`
	Notes       string    `json:"notes,omitempty"`
	Location    string    `json:"location,omitempty"`
	Tags        string    `json:"tags,omitempty"`
	ImageURL    string    `json:"image_url,omitempty"`
}

// TransactionResponse is used for returning transaction data
type TransactionResponse struct {
	ID          uint      `json:"id"`
	UserID      uint      `json:"user_id"`
	AccountID   uint      `json:"account_id"`
	CategoryID  uint      `json:"category_id"`
	Amount      float64   `json:"amount"`
	Type        string    `json:"type"`
	Date        time.Time `json:"date"`
	Description string    `json:"description,omitempty"`
	Notes       string    `json:"notes,omitempty"`
	Location    string    `json:"location,omitempty"`
	Tags        string    `json:"tags,omitempty"`
	ImageURL    string    `json:"image_url,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	
	// Embedded related data
	Account  *AccountResponse  `json:"account,omitempty"`
	Category *CategoryResponse `json:"category,omitempty"`
}

// TransactionListParams is used for filtering transactions
type TransactionListParams struct {
	UserID     uint       `form:"user_id"`
	AccountID  uint       `form:"account_id"`
	CategoryID uint       `form:"category_id"`
	Type       string     `form:"type"`
	StartDate  *time.Time `form:"start_date"`
	EndDate    *time.Time `form:"end_date"`
	MinAmount  *float64   `form:"min_amount"`
	MaxAmount  *float64   `form:"max_amount"`
	Search     string     `form:"search"`
	Page       int        `form:"page,default=1"`
	Limit      int        `form:"limit,default=20"`
	SortBy     string     `form:"sort_by,default=date"`
	SortOrder  string     `form:"sort_order,default=desc"`
}

// ToResponse converts a Transaction to TransactionResponse
func (t *Transaction) ToResponse() TransactionResponse {
	return TransactionResponse{
		ID:          t.ID,
		UserID:      t.UserID,
		AccountID:   t.AccountID,
		CategoryID:  t.CategoryID,
		Amount:      t.Amount,
		Type:        t.Type,
		Date:        t.Date,
		Description: t.Description,
		Notes:       t.Notes,
		Location:    t.Location,
		Tags:        t.Tags,
		ImageURL:    t.ImageURL,
		CreatedAt:   t.CreatedAt,
		UpdatedAt:   t.UpdatedAt,
	}
}

// ToResponseWithRelations converts a Transaction to TransactionResponse with related data
func (t *Transaction) ToResponseWithRelations() TransactionResponse {
	response := t.ToResponse()
	
	accountResponse := t.Account.ToResponse()
	categoryResponse := t.Category.ToResponse()
	
	response.Account = &accountResponse
	response.Category = &categoryResponse
	
	return response
}
