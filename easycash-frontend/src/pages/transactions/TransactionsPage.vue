<template>
  <q-page padding>
    <page-header title="Transactions">
      <template v-slot:actions>
        <q-btn
          color="primary"
          icon="add"
          label="New Transaction"
          to="/transactions/new"
        />
      </template>
    </page-header>
    
    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading" message="Loading transactions..." />
    
    <!-- Transaction Filters -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-3">
            <q-select
              v-model="filters.type"
              :options="typeOptions"
              label="Type"
              emit-value
              map-options
              clearable
              dense
              outlined
            />
          </div>
          
          <div class="col-12 col-md-3">
            <q-select
              v-model="filters.account_id"
              :options="accountOptions"
              label="Account"
              emit-value
              map-options
              clearable
              dense
              outlined
            />
          </div>
          
          <div class="col-12 col-md-3">
            <q-select
              v-model="filters.category_id"
              :options="categoryOptions"
              label="Category"
              emit-value
              map-options
              clearable
              dense
              outlined
            />
          </div>
          
          <div class="col-12 col-md-3">
            <q-input
              v-model="filters.search"
              label="Search"
              dense
              outlined
              clearable
            >
              <template v-slot:append>
                <q-icon name="search" />
              </template>
            </q-input>
          </div>
          
          <div class="col-12 col-md-3">
            <q-input
              v-model="filters.start_date"
              label="Start Date"
              dense
              outlined
              clearable
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                    <q-date v-model="filters.start_date" mask="YYYY-MM-DD" />
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>
          
          <div class="col-12 col-md-3">
            <q-input
              v-model="filters.end_date"
              label="End Date"
              dense
              outlined
              clearable
            >
              <template v-slot:append>
                <q-icon name="event" class="cursor-pointer">
                  <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                    <q-date v-model="filters.end_date" mask="YYYY-MM-DD" />
                  </q-popup-proxy>
                </q-icon>
              </template>
            </q-input>
          </div>
          
          <div class="col-12 col-md-3">
            <q-input
              v-model.number="filters.min_amount"
              label="Min Amount"
              type="number"
              dense
              outlined
              clearable
            />
          </div>
          
          <div class="col-12 col-md-3">
            <q-input
              v-model.number="filters.max_amount"
              label="Max Amount"
              type="number"
              dense
              outlined
              clearable
            />
          </div>
        </div>
        
        <div class="row q-mt-md">
          <div class="col-12">
            <q-btn
              color="primary"
              label="Apply Filters"
              @click="fetchTransactions"
              :loading="loading"
              class="q-mr-sm"
            />
            <q-btn
              color="secondary"
              flat
              label="Reset Filters"
              @click="resetFilters"
              :disable="loading"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>
    
    <!-- Transaction Summary -->
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-12 col-md-4">
        <q-card class="bg-primary text-white">
          <q-card-section>
            <div class="text-subtitle1">Total Transactions</div>
            <div class="text-h4">{{ totalTransactions }}</div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-12 col-md-4">
        <q-card class="bg-positive text-white">
          <q-card-section>
            <div class="text-subtitle1">Total Income</div>
            <div class="text-h4">{{ formatCurrency(totalIncome) }}</div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-12 col-md-4">
        <q-card class="bg-negative text-white">
          <q-card-section>
            <div class="text-subtitle1">Total Expenses</div>
            <div class="text-h4">{{ formatCurrency(totalExpense) }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    
    <!-- Transaction List -->
    <q-card>
      <q-card-section>
        <transaction-list
          :transactions="transactions"
          @select="viewTransaction"
          @edit="editTransaction"
          @delete="confirmDeleteTransaction"
        />
      </q-card-section>
      
      <q-card-section v-if="totalPages > 1">
        <div class="row justify-between items-center">
          <div class="col-auto">
            <span class="text-caption">
              Showing {{ (pagination.page - 1) * pagination.limit + 1 }} to 
              {{ Math.min(pagination.page * pagination.limit, totalTransactions) }} of 
              {{ totalTransactions }} transactions
            </span>
          </div>
          
          <div class="col-auto">
            <q-pagination
              v-model="pagination.page"
              :max="totalPages"
              :max-pages="6"
              boundary-links
              direction-links
              @update:model-value="fetchTransactions"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>
    
    <!-- Delete Confirmation Dialog -->
    <confirm-dialog
      v-model="showDeleteDialog"
      title="Delete Transaction"
      message="Are you sure you want to delete this transaction? This action cannot be undone."
      confirm-label="Delete"
      confirm-color="negative"
      :loading="deleteLoading"
      @confirm="deleteTransaction"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import ConfirmDialog from 'src/components/common/ConfirmDialog.vue';
import TransactionList from 'src/components/transactions/TransactionList.vue';
import { useTransactionStore } from 'src/stores/transaction.store';
import { useAccountStore } from 'src/stores/account.store';
import { useCategoryStore } from 'src/stores/category.store';
import { TransactionListParams } from 'src/services/transaction.service';
import NotificationService from 'src/services/notification.service';

const router = useRouter();
const route = useRoute();
const transactionStore = useTransactionStore();
const accountStore = useAccountStore();
const categoryStore = useCategoryStore();

const loading = ref(false);
const error = ref<string | null>(null);
const showDeleteDialog = ref(false);
const deleteLoading = ref(false);
const transactionToDelete = ref<number | null>(null);

// Pagination
const pagination = ref({
  page: 1,
  limit: 20
});

// Filters
const filters = ref<TransactionListParams>({
  type: '',
  account_id: 0,
  category_id: 0,
  start_date: '',
  end_date: '',
  min_amount: undefined,
  max_amount: undefined,
  search: '',
  page: 1,
  limit: 20,
  sort_by: 'date',
  sort_order: 'desc'
});

// Computed properties
const transactions = computed(() => transactionStore.transactions);
const totalTransactions = computed(() => transactionStore.pagination.total);
const totalIncome = computed(() => transactionStore.totalIncome);
const totalExpense = computed(() => transactionStore.totalExpense);
const totalPages = computed(() => Math.ceil(totalTransactions.value / pagination.value.limit));

// Filter options
const typeOptions = [
  { label: 'All Types', value: '' },
  { label: 'Expense', value: 'expense' },
  { label: 'Income', value: 'income' },
  { label: 'Transfer', value: 'transfer' }
];

const accountOptions = computed(() => {
  return [
    { label: 'All Accounts', value: 0 },
    ...accountStore.accounts.map(account => ({
      label: account.name,
      value: account.id
    }))
  ];
});

const categoryOptions = computed(() => {
  return [
    { label: 'All Categories', value: 0 },
    ...categoryStore.categories.map(category => ({
      label: category.name,
      value: category.id
    }))
  ];
});

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Fetch transactions
const fetchTransactions = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    // Update pagination in filters
    filters.value.page = pagination.value.page;
    filters.value.limit = pagination.value.limit;
    
    // Remove empty filters
    const cleanFilters = { ...filters.value };
    Object.keys(cleanFilters).forEach(key => {
      const value = cleanFilters[key as keyof TransactionListParams];
      if (value === '' || value === 0 || value === undefined) {
        delete cleanFilters[key as keyof TransactionListParams];
      }
    });
    
    await transactionStore.list(cleanFilters);
  } catch (err: any) {
    error.value = 'Failed to load transactions. Please try again.';
    NotificationService.error('Failed to load transactions');
    console.error('Transactions error:', err);
  } finally {
    loading.value = false;
  }
};

// Reset filters
const resetFilters = () => {
  filters.value = {
    type: '',
    account_id: 0,
    category_id: 0,
    start_date: '',
    end_date: '',
    min_amount: undefined,
    max_amount: undefined,
    search: '',
    page: 1,
    limit: 20,
    sort_by: 'date',
    sort_order: 'desc'
  };
  
  pagination.value.page = 1;
  fetchTransactions();
};

// View transaction
const viewTransaction = (id: number) => {
  router.push(`/transactions/${id}`);
};

// Edit transaction
const editTransaction = (id: number) => {
  router.push(`/transactions/${id}/edit`);
};

// Confirm delete transaction
const confirmDeleteTransaction = (id: number) => {
  transactionToDelete.value = id;
  showDeleteDialog.value = true;
};

// Delete transaction
const deleteTransaction = async () => {
  if (!transactionToDelete.value) return;
  
  deleteLoading.value = true;
  
  try {
    await transactionStore.delete(transactionToDelete.value);
    NotificationService.success('Transaction deleted successfully');
    showDeleteDialog.value = false;
  } catch (err: any) {
    error.value = 'Failed to delete transaction. Please try again.';
    NotificationService.error('Failed to delete transaction');
    console.error('Delete transaction error:', err);
  } finally {
    deleteLoading.value = false;
  }
};

// Watch route query parameters
watch(() => route.query, (newQuery) => {
  // Update filters from query parameters
  if (newQuery.account_id) {
    filters.value.account_id = Number(newQuery.account_id);
  }
  
  if (newQuery.category_id) {
    filters.value.category_id = Number(newQuery.category_id);
  }
  
  if (newQuery.type) {
    filters.value.type = String(newQuery.type);
  }
}, { immediate: true });

// Load data on component mount
onMounted(async () => {
  // Load accounts and categories if not already loaded
  if (accountStore.accounts.length === 0) {
    await accountStore.fetchAccounts();
  }
  
  if (categoryStore.categories.length === 0) {
    await categoryStore.fetchCategories();
  }
  
  fetchTransactions();
});
</script>
