import { defineStore } from 'pinia';
import { ref } from 'vue';
import { api } from 'src/boot/axios';
import type { Account } from 'src/types/account';

export const useAccountsStore = defineStore('accounts', () => {
  const accounts = ref<Account[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const fetchAccounts = async () => {
    loading.value = true;
    error.value = null;

    try {
      const response = await api.get('/accounts');
      accounts.value = response.data;

      return accounts.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'An unknown error occurred';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    accounts,
    loading,
    error,
    fetchAccounts,
  };
});
