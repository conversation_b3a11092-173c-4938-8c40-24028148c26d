// 基础响应接口
export interface BaseResponse {
  code: number
  message: string
  data?: unknown
}

export interface PaginatedResponse<T> extends BaseResponse {
  data: {
    items: T[]
    total: number
    page: number
    pageSize: number
  }
}

export interface ListParams {
  page?: number
  pageSize?: number
  [key: string]: number | string | undefined
}

// API错误码枚举
export const API_ERROR_CODES = {
  SUCCESS: 0,
  UNKNOWN_ERROR: -1,
  VALIDATION_ERROR: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
} as const

// API错误码类型
export type APIErrorCode = typeof API_ERROR_CODES[keyof typeof API_ERROR_CODES]

// API错误响应接口
export interface APIErrorResponse extends BaseResponse {
  code: APIErrorCode
  data?: unknown
}

// API成功响应接口
export interface APISuccessResponse<T = unknown> extends BaseResponse {
  code: 0
  data: T
}

// API响应类型
export type APIResponse<T = unknown> = APISuccessResponse<T> | APIErrorResponse