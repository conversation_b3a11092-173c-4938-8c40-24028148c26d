<template>
  <div>
    <q-card-section class="text-center">
      <div class="text-h5 q-mb-lg">Sign In</div>
    </q-card-section>

    <q-card-section>
      <!-- Error Display -->
      <q-banner v-if="error" class="bg-negative text-white q-mb-md" rounded>
        <template v-slot:avatar>
          <q-icon name="error" />
        </template>
        <div class="text-body1">{{ error }}</div>
        <template v-slot:action>
          <q-btn flat color="white" label="Dismiss" @click="error = null" />
        </template>
      </q-banner>

      <!-- Simple Test Login -->
      <div class="q-gutter-md">
        <q-input
          v-model="form.email"
          label="Email"
          type="email"
        >
          <template v-slot:prepend>
            <q-icon name="email" />
          </template>
        </q-input>

        <q-input
          v-model="form.password"
          label="Password"
          :type="showPassword ? 'text' : 'password'"
        >
          <template v-slot:prepend>
            <q-icon name="lock" />
          </template>
          <template v-slot:append>
            <q-icon
              :name="showPassword ? 'visibility_off' : 'visibility'"
              class="cursor-pointer"
              @click="showPassword = !showPassword"
            />
          </template>
        </q-input>

        <div class="row q-mt-md">
          <q-btn
            color="primary"
            label="Sign In"
            class="full-width"
            :loading="loading"
            @click="onSubmit"
          />
        </div>

        <!-- Test Button -->
        <div class="row q-mt-md">
          <q-btn
            color="secondary"
            label="Test Login (Skip Auth)"
            class="full-width"
            @click="testLogin"
          />
        </div>
      </div>
    </q-card-section>

    <q-card-section class="text-center">
      <div class="q-mb-md">
        <router-link to="/auth/forgot-password" class="text-primary">
          Forgot Password?
        </router-link>
      </div>
      <div>
        Don't have an account?
        <router-link to="/auth/register" class="text-primary">
          Sign Up
        </router-link>
      </div>
    </q-card-section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

const loading = ref(false);
const error = ref<string | null>(null);
const showPassword = ref(false);

const form = ref({
  email: '<EMAIL>',
  password: 'password123'
});

const onSubmit = async () => {
  loading.value = true;
  error.value = null;

  try {
    // Simulate login
    console.log('Attempting login with:', form.value);

    // For testing, just set a token in localStorage
    localStorage.setItem('token', 'test-token-123');

    console.log('Login successful, redirecting...');

    // Redirect to the requested page or dashboard
    const redirectPath = route.query.redirect as string || '/';
    router.push(redirectPath);
  } catch (err: any) {
    error.value = 'Login failed. Please try again.';
    console.error('Login error:', err);
  } finally {
    loading.value = false;
  }
};

const testLogin = () => {
  console.log('Test login - setting token and redirecting');
  localStorage.setItem('token', 'test-token-123');
  router.push('/');
};
</script>

<style scoped>
.login-card {
  width: 100%;
  max-width: 400px;
  padding: 20px;
}
</style>
