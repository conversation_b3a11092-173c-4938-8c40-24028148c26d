package controller

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/imaojia/easycash/internal/middleware"
	"github.com/imaojia/easycash/internal/pkg/response"
	"github.com/imaojia/easycash/internal/service"
)

// StatisticsController handles statistics related requests
type StatisticsController struct {
	statisticsService *service.StatisticsService
}

// NewStatisticsController creates a new statistics controller
func NewStatisticsController(statisticsService *service.StatisticsService) *StatisticsController {
	return &StatisticsController{statisticsService: statisticsService}
}

// GetDailyStatistics retrieves daily statistics for the current user
// @Summary Get daily statistics
// @Description Get daily income and expense statistics for the current user
// @Tags statistics
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param start_date query string true "Start date (YYYY-MM-DD)"
// @Param end_date query string true "End date (YYYY-MM-DD)"
// @Success 200 {object} response.Response{data=[]service.DailyStatistics}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/statistics/daily [get]
func (c *StatisticsController) GetDailyStatistics(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	// Parse date parameters
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		response.BadRequest(ctx, "start_date and end_date are required")
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		response.BadRequest(ctx, "invalid start_date format, use YYYY-MM-DD")
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		response.BadRequest(ctx, "invalid end_date format, use YYYY-MM-DD")
		return
	}

	// Limit the date range to prevent performance issues
	maxDays := 90
	if endDate.Sub(startDate).Hours()/24 > float64(maxDays) {
		response.BadRequest(ctx, "date range cannot exceed 90 days")
		return
	}

	statistics, err := c.statisticsService.GetDailyStatistics(userID, startDate, endDate)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, statistics)
}

// GetMonthlyStatistics retrieves monthly statistics for the current user
// @Summary Get monthly statistics
// @Description Get monthly income and expense statistics for the current user
// @Tags statistics
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param start_date query string true "Start date (YYYY-MM)"
// @Param end_date query string true "End date (YYYY-MM)"
// @Success 200 {object} response.Response{data=[]service.MonthlyStatistics}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/statistics/monthly [get]
func (c *StatisticsController) GetMonthlyStatistics(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	// Parse date parameters
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		response.BadRequest(ctx, "start_date and end_date are required")
		return
	}

	startDate, err := time.Parse("2006-01", startDateStr)
	if err != nil {
		response.BadRequest(ctx, "invalid start_date format, use YYYY-MM")
		return
	}

	endDate, err := time.Parse("2006-01", endDateStr)
	if err != nil {
		response.BadRequest(ctx, "invalid end_date format, use YYYY-MM")
		return
	}
	// Set end date to the end of the month
	endDate = time.Date(endDate.Year(), endDate.Month()+1, 0, 23, 59, 59, *********, endDate.Location())

	// Limit the date range to prevent performance issues
	maxMonths := 36
	months := (endDate.Year()-startDate.Year())*12 + int(endDate.Month()-startDate.Month()) + 1
	if months > maxMonths {
		response.BadRequest(ctx, "date range cannot exceed 36 months")
		return
	}

	statistics, err := c.statisticsService.GetMonthlyStatistics(userID, startDate, endDate)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, statistics)
}

// GetYearlyStatistics retrieves yearly statistics for the current user
// @Summary Get yearly statistics
// @Description Get yearly income and expense statistics for the current user
// @Tags statistics
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param start_year query int true "Start year"
// @Param end_year query int true "End year"
// @Success 200 {object} response.Response{data=[]service.YearlyStatistics}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/statistics/yearly [get]
func (c *StatisticsController) GetYearlyStatistics(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	// Parse year parameters
	startYearStr := ctx.Query("start_year")
	endYearStr := ctx.Query("end_year")

	if startYearStr == "" || endYearStr == "" {
		response.BadRequest(ctx, "start_year and end_year are required")
		return
	}

	startYear, err := strconv.Atoi(startYearStr)
	if err != nil {
		response.BadRequest(ctx, "invalid start_year")
		return
	}

	endYear, err := strconv.Atoi(endYearStr)
	if err != nil {
		response.BadRequest(ctx, "invalid end_year")
		return
	}

	// Limit the year range to prevent performance issues
	maxYears := 10
	if endYear-startYear+1 > maxYears {
		response.BadRequest(ctx, "year range cannot exceed 10 years")
		return
	}

	statistics, err := c.statisticsService.GetYearlyStatistics(userID, startYear, endYear)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, statistics)
}

// GetCategoryStatistics retrieves category statistics for the current user
// @Summary Get category statistics
// @Description Get category statistics for the current user
// @Tags statistics
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param type query string false "Transaction type (income or expense)"
// @Param start_date query string true "Start date (YYYY-MM-DD)"
// @Param end_date query string true "End date (YYYY-MM-DD)"
// @Success 200 {object} response.Response{data=[]service.CategoryStatistics}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/statistics/category [get]
func (c *StatisticsController) GetCategoryStatistics(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	// Parse parameters
	transactionType := ctx.Query("type")
	startDateStr := ctx.Query("start_date")
	endDateStr := ctx.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		response.BadRequest(ctx, "start_date and end_date are required")
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		response.BadRequest(ctx, "invalid start_date format, use YYYY-MM-DD")
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		response.BadRequest(ctx, "invalid end_date format, use YYYY-MM-DD")
		return
	}
	// Set end date to the end of the day
	endDate = time.Date(endDate.Year(), endDate.Month(), endDate.Day(), 23, 59, 59, *********, endDate.Location())

	// Validate transaction type if provided
	if transactionType != "" && transactionType != "income" && transactionType != "expense" {
		response.BadRequest(ctx, "type must be 'income' or 'expense'")
		return
	}

	statistics, err := c.statisticsService.GetCategoryStatistics(userID, transactionType, startDate, endDate)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, statistics)
}

// GetAccountStatistics retrieves account statistics for the current user
// @Summary Get account statistics
// @Description Get account balance statistics for the current user
// @Tags statistics
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=[]service.AccountStatistics}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/statistics/account [get]
func (c *StatisticsController) GetAccountStatistics(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	statistics, err := c.statisticsService.GetAccountStatistics(userID)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, statistics)
}
