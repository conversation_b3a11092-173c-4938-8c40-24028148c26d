package service

import (
	"errors"
	"time"

	"gorm.io/gorm"

	"github.com/imaojia/easycash/internal/model"
)

// TransactionService handles transaction related operations
type TransactionService struct {
	db             *gorm.DB
	accountService *AccountService
}

// NewTransactionService creates a new transaction service
func NewTransactionService(db *gorm.DB, accountService *AccountService) *TransactionService {
	return &TransactionService{
		db:             db,
		accountService: accountService,
	}
}

// CreateTransaction creates a new transaction for a user
func (s *TransactionService) CreateTransaction(userID uint, req model.TransactionRequest) (*model.TransactionResponse, error) {
	// Verify account belongs to user
	account, err := s.accountService.GetAccountByID(userID, req.AccountID)
	if err != nil {
		return nil, errors.New("invalid account")
	}

	// Verify category belongs to user
	var category model.Category
	if err := s.db.Where("id = ? AND user_id = ?", req.CategoryID, userID).First(&category).Error; err != nil {
		return nil, errors.New("invalid category")
	}

	// Create transaction
	transaction := model.Transaction{
		UserID:      userID,
		AccountID:   req.AccountID,
		CategoryID:  req.CategoryID,
		Amount:      req.Amount,
		Type:        req.Type,
		Date:        req.Date,
		Description: req.Description,
		Notes:       req.Notes,
		Location:    req.Location,
		Tags:        req.Tags,
		ImageURL:    req.ImageURL,
	}

	// Start a transaction
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// Save the transaction
		if err := tx.Create(&transaction).Error; err != nil {
			return err
		}

		// Update account balance
		var balanceChange float64
		if transaction.Type == "expense" {
			balanceChange = -transaction.Amount
		} else if transaction.Type == "income" {
			balanceChange = transaction.Amount
		} else if transaction.Type == "transfer" {
			// For transfers, we would need to handle the destination account as well
			// This is a simplified implementation
			balanceChange = -transaction.Amount
		}

		if err := tx.Model(&model.Account{}).Where("id = ?", account.ID).
			UpdateColumn("balance", gorm.Expr("balance + ?", balanceChange)).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Load relations for response
	if err := s.db.Preload("Account").Preload("Category").First(&transaction, transaction.ID).Error; err != nil {
		return nil, err
	}

	// Convert to response
	accountResponse := transaction.Account.ToResponse()
	categoryResponse := transaction.Category.ToResponse()

	return &model.TransactionResponse{
		ID:          transaction.ID,
		UserID:      transaction.UserID,
		AccountID:   transaction.AccountID,
		CategoryID:  transaction.CategoryID,
		Amount:      transaction.Amount,
		Type:        transaction.Type,
		Date:        transaction.Date,
		Description: transaction.Description,
		Notes:       transaction.Notes,
		Location:    transaction.Location,
		Tags:        transaction.Tags,
		ImageURL:    transaction.ImageURL,
		CreatedAt:   transaction.CreatedAt,
		UpdatedAt:   transaction.UpdatedAt,
		Account:     &accountResponse,
		Category:    &categoryResponse,
	}, nil
}

// GetTransactionByID retrieves a transaction by ID
func (s *TransactionService) GetTransactionByID(userID, transactionID uint) (*model.Transaction, error) {
	var transaction model.Transaction
	if err := s.db.Preload("Account").Preload("Category").
		Where("id = ? AND user_id = ?", transactionID, userID).
		First(&transaction).Error; err != nil {
		return nil, err
	}
	return &transaction, nil
}

// UpdateTransaction updates a transaction
func (s *TransactionService) UpdateTransaction(userID, transactionID uint, req model.TransactionRequest) (*model.TransactionResponse, error) {
	// Get the transaction
	transaction, err := s.GetTransactionByID(userID, transactionID)
	if err != nil {
		return nil, err
	}

	// Verify account belongs to user
	if _, err := s.accountService.GetAccountByID(userID, req.AccountID); err != nil {
		return nil, errors.New("invalid account")
	}

	// Verify category belongs to user
	var category model.Category
	if err := s.db.Where("id = ? AND user_id = ?", req.CategoryID, userID).First(&category).Error; err != nil {
		return nil, errors.New("invalid category")
	}

	// Calculate balance adjustment
	var oldBalanceChange, newBalanceChange float64
	if transaction.Type == "expense" {
		oldBalanceChange = transaction.Amount
	} else if transaction.Type == "income" {
		oldBalanceChange = -transaction.Amount
	}

	if req.Type == "expense" {
		newBalanceChange = -req.Amount
	} else if req.Type == "income" {
		newBalanceChange = req.Amount
	}

	// Start a transaction
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// If account is changing, update both old and new account balances
		if transaction.AccountID != req.AccountID {
			// Revert the effect on the old account
			if err := tx.Model(&model.Account{}).Where("id = ?", transaction.AccountID).
				UpdateColumn("balance", gorm.Expr("balance + ?", oldBalanceChange)).Error; err != nil {
				return err
			}

			// Apply the effect on the new account
			if err := tx.Model(&model.Account{}).Where("id = ?", req.AccountID).
				UpdateColumn("balance", gorm.Expr("balance + ?", newBalanceChange)).Error; err != nil {
				return err
			}
		} else {
			// Same account, just update with the difference
			balanceAdjustment := newBalanceChange + oldBalanceChange
			if err := tx.Model(&model.Account{}).Where("id = ?", transaction.AccountID).
				UpdateColumn("balance", gorm.Expr("balance + ?", balanceAdjustment)).Error; err != nil {
				return err
			}
		}

		// Update transaction fields
		transaction.AccountID = req.AccountID
		transaction.CategoryID = req.CategoryID
		transaction.Amount = req.Amount
		transaction.Type = req.Type
		transaction.Date = req.Date
		transaction.Description = req.Description
		transaction.Notes = req.Notes
		transaction.Location = req.Location
		transaction.Tags = req.Tags
		transaction.ImageURL = req.ImageURL

		// Save the transaction
		if err := tx.Save(transaction).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Reload the transaction with relations
	if err := s.db.Preload("Account").Preload("Category").First(transaction, transaction.ID).Error; err != nil {
		return nil, err
	}

	// Convert to response
	accountResponse := transaction.Account.ToResponse()
	categoryResponse := transaction.Category.ToResponse()

	return &model.TransactionResponse{
		ID:          transaction.ID,
		UserID:      transaction.UserID,
		AccountID:   transaction.AccountID,
		CategoryID:  transaction.CategoryID,
		Amount:      transaction.Amount,
		Type:        transaction.Type,
		Date:        transaction.Date,
		Description: transaction.Description,
		Notes:       transaction.Notes,
		Location:    transaction.Location,
		Tags:        transaction.Tags,
		ImageURL:    transaction.ImageURL,
		CreatedAt:   transaction.CreatedAt,
		UpdatedAt:   transaction.UpdatedAt,
		Account:     &accountResponse,
		Category:    &categoryResponse,
	}, nil
}

// DeleteTransaction deletes a transaction
func (s *TransactionService) DeleteTransaction(userID, transactionID uint) error {
	// Get the transaction
	transaction, err := s.GetTransactionByID(userID, transactionID)
	if err != nil {
		return err
	}

	// Calculate balance adjustment
	var balanceChange float64
	if transaction.Type == "expense" {
		balanceChange = transaction.Amount
	} else if transaction.Type == "income" {
		balanceChange = -transaction.Amount
	}

	// Start a transaction
	return s.db.Transaction(func(tx *gorm.DB) error {
		// Update account balance
		if err := tx.Model(&model.Account{}).Where("id = ?", transaction.AccountID).
			UpdateColumn("balance", gorm.Expr("balance + ?", balanceChange)).Error; err != nil {
			return err
		}

		// Delete the transaction
		if err := tx.Delete(transaction).Error; err != nil {
			return err
		}

		return nil
	})
}

// ListTransactions retrieves transactions for a user
func (s *TransactionService) ListTransactions(params model.TransactionListParams) ([]model.Transaction, int64, error) {
	var transactions []model.Transaction
	var total int64

	// Build query
	query := s.db.Model(&model.Transaction{}).Where("user_id = ?", params.UserID)

	// Apply filters
	if params.AccountID != 0 {
		query = query.Where("account_id = ?", params.AccountID)
	}

	if params.CategoryID != 0 {
		query = query.Where("category_id = ?", params.CategoryID)
	}

	if params.Type != "" {
		query = query.Where("type = ?", params.Type)
	}

	if params.StartDate != nil {
		query = query.Where("date >= ?", params.StartDate)
	}

	if params.EndDate != nil {
		query = query.Where("date <= ?", params.EndDate)
	}

	if params.MinAmount != nil {
		query = query.Where("amount >= ?", params.MinAmount)
	}

	if params.MaxAmount != nil {
		query = query.Where("amount <= ?", params.MaxAmount)
	}

	if params.Search != "" {
		query = query.Where("description LIKE ? OR notes LIKE ? OR location LIKE ? OR tags LIKE ?",
			"%"+params.Search+"%", "%"+params.Search+"%", "%"+params.Search+"%", "%"+params.Search+"%")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if params.SortBy != "" {
		order := "ASC"
		if params.SortOrder == "desc" {
			order = "DESC"
		}
		query = query.Order(params.SortBy + " " + order)
	} else {
		query = query.Order("date DESC")
	}

	// Apply pagination
	offset := (params.Page - 1) * params.Limit
	query = query.Offset(offset).Limit(params.Limit)

	// Preload relations
	query = query.Preload("Account").Preload("Category")

	// Execute query
	if err := query.Find(&transactions).Error; err != nil {
		return nil, 0, err
	}

	return transactions, total, nil
}

// GetTransactionsByDateRange retrieves transactions for a user within a date range
func (s *TransactionService) GetTransactionsByDateRange(userID uint, startDate, endDate time.Time) ([]model.Transaction, error) {
	var transactions []model.Transaction

	if err := s.db.Where("user_id = ? AND date BETWEEN ? AND ?", userID, startDate, endDate).
		Preload("Account").Preload("Category").
		Find(&transactions).Error; err != nil {
		return nil, err
	}

	return transactions, nil
}
