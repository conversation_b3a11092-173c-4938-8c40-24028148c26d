package model

import (
	"time"

	"gorm.io/gorm"
)

// Account represents a financial account in the system
type Account struct {
	ID        uint      `json:"id" gorm:"primarykey"`
	UserID    uint      `json:"user_id" gorm:"not null;index"`
	Name      string    `json:"name" gorm:"size:50;not null"`
	Type      string    `json:"type" gorm:"size:20;not null"` // cash, bank, credit, etc.
	Balance   float64   `json:"balance" gorm:"type:decimal(10,2);not null;default:0"`
	Currency  string    `json:"currency" gorm:"size:10;not null;default:'CNY'"`
	Icon      string    `json:"icon,omitempty" gorm:"size:50"`
	Color     string    `json:"color,omitempty" gorm:"size:20"`
	IsDefault bool      `json:"is_default" gorm:"default:false"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relations
	User         User          `json:"-" gorm:"foreignKey:UserID"`
	Transactions []Transaction `json:"-" gorm:"foreignKey:AccountID"`
}

// AccountRequest is used for creating or updating an account
type AccountRequest struct {
	Name      string  `json:"name" binding:"required,min=1,max=50"`
	Type      string  `json:"type" binding:"required"`
	Balance   float64 `json:"balance"`
	Currency  string  `json:"currency" binding:"required"`
	Icon      string  `json:"icon,omitempty"`
	Color     string  `json:"color,omitempty"`
	IsDefault bool    `json:"is_default"`
}

// AccountResponse is used for returning account data
type AccountResponse struct {
	ID        uint      `json:"id"`
	UserID    uint      `json:"user_id"`
	Name      string    `json:"name"`
	Type      string    `json:"type"`
	Balance   float64   `json:"balance"`
	Currency  string    `json:"currency"`
	Icon      string    `json:"icon,omitempty"`
	Color     string    `json:"color,omitempty"`
	IsDefault bool      `json:"is_default"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// AccountListParams is used for filtering accounts
type AccountListParams struct {
	UserID uint   `form:"user_id"`
	Type   string `form:"type"`
	Page   int    `form:"page,default=1"`
	Limit  int    `form:"limit,default=10"`
}

// ToResponse converts an Account to AccountResponse
func (a *Account) ToResponse() AccountResponse {
	return AccountResponse{
		ID:        a.ID,
		UserID:    a.UserID,
		Name:      a.Name,
		Type:      a.Type,
		Balance:   a.Balance,
		Currency:  a.Currency,
		Icon:      a.Icon,
		Color:     a.Color,
		IsDefault: a.IsDefault,
		CreatedAt: a.CreatedAt,
		UpdatedAt: a.UpdatedAt,
	}
}
