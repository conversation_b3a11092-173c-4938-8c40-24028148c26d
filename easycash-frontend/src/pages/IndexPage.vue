<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-md">
      <!-- Welcome section -->
      <div class="col-12">
        <q-card class="bg-primary text-white">
          <q-card-section>
            <div class="text-h6">Welcome back, {{ username }}</div>
            <div class="text-subtitle2">Here's your financial summary</div>
          </q-card-section>
        </q-card>
      </div>

      <!-- Account summary cards -->
      <div v-for="account in accounts" :key="account.id" class="col-12 col-md-4">
        <q-card class="account-card">
          <q-card-section>
            <div class="row items-center no-wrap">
              <div class="col">
                <div class="text-subtitle1 text-grey-7">{{ account.name }}</div>
                <div class="text-h5 q-mt-sm q-mb-xs">{{ formatCurrency(account.balance) }}</div>
                <div class="text-caption text-grey">{{ account.type }}</div>
              </div>
              <div class="col-auto">
                <q-icon :name="getAccountIcon(account.type)" size="3rem" color="primary" />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- Quick actions -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md">Quick Actions</div>
            <div class="row q-col-gutter-md">
              <div class="col-6 col-md-3">
                <q-btn class="full-width q-py-sm" color="primary" icon="add" label="New Transaction" to="/transactions" />
              </div>
              <div class="col-6 col-md-3">
                <q-btn class="full-width q-py-sm" color="secondary" icon="account_balance_wallet" label="Manage Accounts" to="/accounts" />
              </div>
              <div class="col-6 col-md-3">
                <q-btn class="full-width q-py-sm" color="deep-orange" icon="pie_chart" label="View Reports" to="/statistics" />
              </div>
              <div class="col-6 col-md-3">
                <q-btn class="full-width q-py-sm" color="teal" icon="savings" label="Budget" to="/budgets" />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- Recent transactions -->
      <div class="col-12 col-md-8">
        <q-card>
          <q-card-section>
            <div class="text-h6">Recent Transactions</div>
          </q-card-section>

          <q-list separator>
            <q-item v-for="transaction in recentTransactions" :key="transaction.id" clickable>
              <q-item-section avatar>
                <q-icon :name="getCategoryIcon(transaction.category)" color="primary" />
              </q-item-section>
              
              <q-item-section>
                <q-item-label>{{ transaction.description }}</q-item-label>
                <q-item-label caption>{{ transaction.date }}</q-item-label>
              </q-item-section>
              
              <q-item-section side>
                <q-item-label :class="transaction.amount < 0 ? 'text-negative' : 'text-positive'">
                  {{ formatCurrency(transaction.amount) }}
                </q-item-label>
                <q-item-label caption>{{ transaction.account }}</q-item-label>
              </q-item-section>
            </q-item>
            
            <q-item v-if="recentTransactions.length === 0">
              <q-item-section>
                <q-item-label class="text-center text-grey-7">No transactions found</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
          
          <q-card-actions align="right">
            <q-btn flat color="primary" label="View All" to="/transactions" />
          </q-card-actions>
        </q-card>
      </div>

      <!-- Spending Overview -->
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">Monthly Spending</div>
            <div class="text-subtitle2 text-grey">Top Categories</div>
          </q-card-section>

          <q-card-section>
            <div v-for="category in topSpendingCategories" :key="category.id" class="q-mb-md">
              <div class="row items-center justify-between q-mb-xs">
                <div class="text-subtitle2">{{ category.name }}</div>
                <div class="text-subtitle2">{{ formatCurrency(category.amount) }}</div>
              </div>
              <q-linear-progress size="10px" :value="category.percentage / 100" :color="category.color" />
            </div>
          </q-card-section>
          
          <q-card-actions align="right">
            <q-btn flat color="primary" label="Full Report" to="/statistics" />
          </q-card-actions>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';

// Mock data - in a real app, this would come from the API
const username = ref('User');
const $q = useQuasar();

// Helper functions
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(value);
};

const getAccountIcon = (type: string) => {
  const icons: Record<string, string> = {
    'Checking': 'account_balance',
    'Savings': 'savings',
    'Credit Card': 'credit_card',
    'Cash': 'payments',
    'Investment': 'trending_up'
  };
  
  return icons[type] || 'account_balance_wallet';
};

const getCategoryIcon = (category: string) => {
  const icons: Record<string, string> = {
    'Food': 'restaurant',
    'Transportation': 'directions_car',
    'Housing': 'home',
    'Entertainment': 'movie',
    'Shopping': 'shopping_bag',
    'Health': 'health_and_safety',
    'Education': 'school',
    'Salary': 'payments'
  };
  
  return icons[category] || 'category';
};

// Mock account data
const accounts = ref([
  { id: 1, name: 'Main Account', type: 'Checking', balance: 3250.65 },
  { id: 2, name: 'Savings', type: 'Savings', balance: 12500.00 },
  { id: 3, name: 'Credit Card', type: 'Credit Card', balance: -450.25 }
]);

// Mock transaction data
const recentTransactions = ref([
  { id: 1, description: 'Grocery Store', category: 'Food', amount: -85.20, date: '2023-09-01', account: 'Main Account' },
  { id: 2, description: 'Monthly Salary', category: 'Salary', amount: 2800.00, date: '2023-08-28', account: 'Main Account' },
  { id: 3, description: 'Restaurant Dinner', category: 'Food', amount: -62.50, date: '2023-08-25', account: 'Credit Card' },
  { id: 4, description: 'Uber Ride', category: 'Transportation', amount: -18.75, date: '2023-08-22', account: 'Credit Card' },
  { id: 5, description: 'Amazon Purchase', category: 'Shopping', amount: -34.99, date: '2023-08-20', account: 'Main Account' }
]);

// Mock spending categories
const topSpendingCategories = ref([
  { id: 1, name: 'Food', amount: 320.45, percentage: 35, color: 'red' },
  { id: 2, name: 'Transportation', amount: 250.30, percentage: 28, color: 'blue' },
  { id: 3, name: 'Entertainment', amount: 180.75, percentage: 20, color: 'purple' },
  { id: 4, name: 'Shopping', amount: 150.25, percentage: 17, color: 'green' }
]);

// In a real app, you would fetch data from the API
onMounted(() => {
  // Simulate API fetch with loading state
  $q.loading.show({
    message: 'Loading your financial data...'
  });
  
  // Simulate API delay
  setTimeout(() => {
    // Here you would update the data from API responses
    username.value = localStorage.getItem('user') 
      ? JSON.parse(localStorage.getItem('user') || '{}').username 
      : 'User';
      
    $q.loading.hide();
  }, 1000);
});
</script>

<style lang="scss" scoped>
.account-card {
  transition: transform 0.3s;
  
  &:hover {
    transform: translateY(-5px);
  }
}
</style>
