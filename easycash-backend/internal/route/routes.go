package route

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/imaojia/easycash/internal/controller"
	"github.com/imaojia/easycash/internal/middleware"
	"github.com/imaojia/easycash/internal/service"
)

// SetupRoutes configures all the routes for the application
func SetupRoutes(r *gin.Engine, db *gorm.DB) {
	// Create services
	authService := service.NewAuthService(db)
	accountService := service.NewAccountService(db)
	categoryService := service.NewCategoryService(db)
	transactionService := service.NewTransactionService(db, accountService)
	budgetService := service.NewBudgetService(db, transactionService)
	statisticsService := service.NewStatisticsService(db)

	// Create controllers
	authController := controller.NewAuthController(authService)
	accountController := controller.NewAccountController(accountService)
	categoryController := controller.NewCategoryController(categoryService)
	transactionController := controller.NewTransactionController(transactionService)
	budgetController := controller.NewBudgetController(budgetService)
	statisticsController := controller.NewStatisticsController(statisticsService)

	// API routes
	api := r.Group("/api")
	{
		// Auth routes
		auth := api.Group("/auth")
		{
			auth.POST("/register", authController.Register)
			auth.POST("/login", authController.Login)

			// Protected auth routes
			authProtected := auth.Group("/")
			authProtected.Use(middleware.Auth())
			{
				authProtected.GET("/profile", authController.GetProfile)
				authProtected.PUT("/profile", authController.UpdateProfile)
				authProtected.POST("/change-password", authController.ChangePassword)
			}
		}

		// Protected routes
		protected := api.Group("/")
		protected.Use(middleware.Auth())
		{
			// Account routes
			accounts := protected.Group("/accounts")
			{
				accounts.GET("", accountController.ListAccounts)
				accounts.POST("", accountController.CreateAccount)
				accounts.GET("/default", accountController.GetDefaultAccount)
				accounts.GET("/:id", accountController.GetAccount)
				accounts.PUT("/:id", accountController.UpdateAccount)
				accounts.DELETE("/:id", accountController.DeleteAccount)
			}

			// Category routes
			categories := protected.Group("/categories")
			{
				categories.GET("", categoryController.ListCategories)
				categories.POST("", categoryController.CreateCategory)
				categories.GET("/hierarchy", categoryController.GetCategoryHierarchy)
				categories.GET("/:id", categoryController.GetCategory)
				categories.GET("/:id/with-children", categoryController.GetCategoryWithChildren)
				categories.PUT("/:id", categoryController.UpdateCategory)
				categories.DELETE("/:id", categoryController.DeleteCategory)
			}

			// Transaction routes
			transactions := protected.Group("/transactions")
			{
				transactions.GET("", transactionController.ListTransactions)
				transactions.POST("", transactionController.CreateTransaction)
				transactions.GET("/:id", transactionController.GetTransaction)
				transactions.PUT("/:id", transactionController.UpdateTransaction)
				transactions.DELETE("/:id", transactionController.DeleteTransaction)
			}

			// Budget routes
			budgets := protected.Group("/budgets")
			{
				budgets.GET("", budgetController.ListBudgets)
				budgets.POST("", budgetController.CreateBudget)
				budgets.GET("/active", budgetController.GetActiveBudgets)
				budgets.GET("/:id", budgetController.GetBudget)
				budgets.PUT("/:id", budgetController.UpdateBudget)
				budgets.DELETE("/:id", budgetController.DeleteBudget)
			}

			// Statistics routes
			statistics := protected.Group("/statistics")
			{
				statistics.GET("/daily", statisticsController.GetDailyStatistics)
				statistics.GET("/monthly", statisticsController.GetMonthlyStatistics)
				statistics.GET("/yearly", statisticsController.GetYearlyStatistics)
				statistics.GET("/category", statisticsController.GetCategoryStatistics)
				statistics.GET("/account", statisticsController.GetAccountStatistics)
			}
		}
	}
}
