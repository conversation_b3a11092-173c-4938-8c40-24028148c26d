package service

import (
	"errors"
	"time"

	"gorm.io/gorm"

	"github.com/imaojia/easycash/internal/model"
)

// BudgetService handles budget related operations
type BudgetService struct {
	db                 *gorm.DB
	transactionService *TransactionService
}

// NewBudgetService creates a new budget service
func NewBudgetService(db *gorm.DB, transactionService *TransactionService) *BudgetService {
	return &BudgetService{
		db:                 db,
		transactionService: transactionService,
	}
}

// CreateBudget creates a new budget for a user
func (s *BudgetService) CreateBudget(userID uint, req model.BudgetRequest) (*model.BudgetResponse, error) {
	// Verify category belongs to user
	var category model.Category
	if err := s.db.Where("id = ? AND user_id = ?", req.CategoryID, userID).First(&category).Error; err != nil {
		return nil, errors.New("invalid category")
	}

	// Verify dates
	if req.StartDate.After(req.EndDate) {
		return nil, errors.New("start date must be before end date")
	}

	// Check for overlapping budgets for the same category
	var count int64
	if err := s.db.Model(&model.Budget{}).
		Where("user_id = ? AND category_id = ? AND ((start_date BETWEEN ? AND ?) OR (end_date BETWEEN ? AND ?) OR (start_date <= ? AND end_date >= ?))",
			userID, req.CategoryID,
			req.StartDate, req.EndDate, req.StartDate, req.EndDate,
			req.StartDate, req.EndDate).
		Count(&count).Error; err != nil {
		return nil, err
	}

	if count > 0 {
		return nil, errors.New("overlapping budget exists for this category and date range")
	}

	// Create budget
	budget := model.Budget{
		UserID:     userID,
		CategoryID: req.CategoryID,
		Amount:     req.Amount,
		Period:     req.Period,
		StartDate:  req.StartDate,
		EndDate:    req.EndDate,
	}

	// Save the budget
	if err := s.db.Create(&budget).Error; err != nil {
		return nil, err
	}

	// Load category for response
	if err := s.db.First(&budget.Category, budget.CategoryID).Error; err != nil {
		return nil, err
	}

	// Calculate spent amount
	spentAmount, err := s.CalculateSpentAmount(userID, budget.CategoryID, budget.StartDate, budget.EndDate)
	if err != nil {
		return nil, err
	}

	// Create response
	response := budget.ToResponseWithRelations()
	response.SpentAmount = spentAmount
	if budget.Amount > 0 {
		response.Percentage = (spentAmount / budget.Amount) * 100
	}

	return &response, nil
}

// GetBudgetByID retrieves a budget by ID
func (s *BudgetService) GetBudgetByID(userID, budgetID uint) (*model.Budget, error) {
	var budget model.Budget
	if err := s.db.Preload("Category").
		Where("id = ? AND user_id = ?", budgetID, userID).
		First(&budget).Error; err != nil {
		return nil, err
	}
	return &budget, nil
}

// UpdateBudget updates a budget
func (s *BudgetService) UpdateBudget(userID, budgetID uint, req model.BudgetRequest) (*model.BudgetResponse, error) {
	// Get the budget
	budget, err := s.GetBudgetByID(userID, budgetID)
	if err != nil {
		return nil, err
	}

	// Verify category belongs to user
	var category model.Category
	if err := s.db.Where("id = ? AND user_id = ?", req.CategoryID, userID).First(&category).Error; err != nil {
		return nil, errors.New("invalid category")
	}

	// Verify dates
	if req.StartDate.After(req.EndDate) {
		return nil, errors.New("start date must be before end date")
	}

	// Check for overlapping budgets for the same category (excluding this budget)
	var count int64
	if err := s.db.Model(&model.Budget{}).
		Where("user_id = ? AND category_id = ? AND id != ? AND ((start_date BETWEEN ? AND ?) OR (end_date BETWEEN ? AND ?) OR (start_date <= ? AND end_date >= ?))",
			userID, req.CategoryID, budgetID,
			req.StartDate, req.EndDate, req.StartDate, req.EndDate,
			req.StartDate, req.EndDate).
		Count(&count).Error; err != nil {
		return nil, err
	}

	if count > 0 {
		return nil, errors.New("overlapping budget exists for this category and date range")
	}

	// Update budget fields
	budget.CategoryID = req.CategoryID
	budget.Amount = req.Amount
	budget.Period = req.Period
	budget.StartDate = req.StartDate
	budget.EndDate = req.EndDate

	// Save the budget
	if err := s.db.Save(budget).Error; err != nil {
		return nil, err
	}

	// Reload category for response
	if err := s.db.First(&budget.Category, budget.CategoryID).Error; err != nil {
		return nil, err
	}

	// Calculate spent amount
	spentAmount, err := s.CalculateSpentAmount(userID, budget.CategoryID, budget.StartDate, budget.EndDate)
	if err != nil {
		return nil, err
	}

	// Create response
	response := budget.ToResponseWithRelations()
	response.SpentAmount = spentAmount
	if budget.Amount > 0 {
		response.Percentage = (spentAmount / budget.Amount) * 100
	}

	return &response, nil
}

// DeleteBudget deletes a budget
func (s *BudgetService) DeleteBudget(userID, budgetID uint) error {
	// Get the budget
	budget, err := s.GetBudgetByID(userID, budgetID)
	if err != nil {
		return err
	}

	// Delete the budget
	return s.db.Delete(budget).Error
}

// ListBudgets retrieves budgets for a user
func (s *BudgetService) ListBudgets(params model.BudgetListParams) ([]model.Budget, int64, error) {
	var budgets []model.Budget
	var total int64

	// Build query
	query := s.db.Model(&model.Budget{}).Where("user_id = ?", params.UserID)

	// Apply filters
	if params.CategoryID != 0 {
		query = query.Where("category_id = ?", params.CategoryID)
	}

	if params.Period != "" {
		query = query.Where("period = ?", params.Period)
	}

	if params.StartDate != nil {
		query = query.Where("start_date >= ?", params.StartDate)
	}

	if params.EndDate != nil {
		query = query.Where("end_date <= ?", params.EndDate)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (params.Page - 1) * params.Limit
	query = query.Offset(offset).Limit(params.Limit)

	// Preload relations
	query = query.Preload("Category")

	// Execute query
	if err := query.Find(&budgets).Error; err != nil {
		return nil, 0, err
	}

	return budgets, total, nil
}

// GetActiveBudgets retrieves active budgets for a user
func (s *BudgetService) GetActiveBudgets(userID uint) ([]model.Budget, error) {
	var budgets []model.Budget
	now := time.Now()

	if err := s.db.Where("user_id = ? AND start_date <= ? AND end_date >= ?", userID, now, now).
		Preload("Category").
		Find(&budgets).Error; err != nil {
		return nil, err
	}

	return budgets, nil
}

// GetBudgetWithStats retrieves a budget with spending statistics
func (s *BudgetService) GetBudgetWithStats(userID, budgetID uint) (*model.BudgetResponse, error) {
	// Get the budget
	budget, err := s.GetBudgetByID(userID, budgetID)
	if err != nil {
		return nil, err
	}

	// Calculate spent amount
	spentAmount, err := s.CalculateSpentAmount(userID, budget.CategoryID, budget.StartDate, budget.EndDate)
	if err != nil {
		return nil, err
	}

	// Create response
	response := budget.ToResponseWithRelations()
	response.SpentAmount = spentAmount
	if budget.Amount > 0 {
		response.Percentage = (spentAmount / budget.Amount) * 100
	}

	return &response, nil
}

// CalculateSpentAmount calculates the total spent amount for a category within a date range
func (s *BudgetService) CalculateSpentAmount(userID, categoryID uint, startDate, endDate time.Time) (float64, error) {
	var result struct {
		Total float64
	}

	err := s.db.Model(&model.Transaction{}).
		Select("SUM(amount) as total").
		Where("user_id = ? AND category_id = ? AND type = ? AND date BETWEEN ? AND ?",
			userID, categoryID, "expense", startDate, endDate).
		Scan(&result).Error

	if err != nil {
		return 0, err
	}

	return result.Total, nil
}
