<template>
  <q-dialog
    v-model="isOpen"
    persistent
    :maximized="maximized"
    transition-show="scale"
    transition-hide="scale"
  >
    <q-card class="confirm-dialog">
      <q-card-section class="row items-center" :class="headerClass">
        <div class="text-h6">{{ title }}</div>
        <q-space />
        <q-btn v-if="showClose" icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-card-section>
        <div v-if="message" class="text-body1 q-mb-md">{{ message }}</div>
        <slot></slot>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          v-if="showCancel"
          :label="cancelLabel"
          color="grey"
          flat
          :disable="loading"
          @click="onCancel"
        />
        <q-btn
          :label="confirmLabel"
          :color="confirmColor"
          :loading="loading"
          @click="onConfirm"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Confirm'
  },
  message: {
    type: String,
    default: 'Are you sure you want to proceed?'
  },
  confirmLabel: {
    type: String,
    default: 'Confirm'
  },
  cancelLabel: {
    type: String,
    default: 'Cancel'
  },
  confirmColor: {
    type: String,
    default: 'primary'
  },
  headerClass: {
    type: String,
    default: ''
  },
  showCancel: {
    type: Boolean,
    default: true
  },
  showClose: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  maximized: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);

const isOpen = ref(props.modelValue);

watch(() => props.modelValue, (newVal) => {
  isOpen.value = newVal;
});

watch(isOpen, (newVal) => {
  emit('update:modelValue', newVal);
});

const onConfirm = () => {
  emit('confirm');
};

const onCancel = () => {
  emit('cancel');
  isOpen.value = false;
};
</script>

<style scoped>
.confirm-dialog {
  min-width: 300px;
}
</style>
