<template>
  <div class="budget-list">
    <q-list separator>
      <q-item
        v-for="budget in budgets"
        :key="budget.id"
        clickable
        @click="$emit('select', budget.id)"
      >
        <q-item-section avatar>
          <q-avatar :color="budget.category?.color || 'primary'" text-color="white">
            <q-icon :name="budget.category?.icon || 'account_balance_wallet'" />
          </q-avatar>
        </q-item-section>
        
        <q-item-section>
          <q-item-label>{{ budget.category?.name || 'Uncategorized' }}</q-item-label>
          <q-item-label caption>
            {{ formatDateRange(budget.start_date, budget.end_date) }} • {{ budget.period }}
          </q-item-label>
          <q-linear-progress
            :value="getBudgetProgress(budget)"
            :color="getBudgetColor(budget)"
            size="md"
            class="q-mt-xs"
          />
        </q-item-section>
        
        <q-item-section side>
          <q-item-label>
            {{ formatCurrency(budget.spent_amount || 0) }} / {{ formatCurrency(budget.amount) }}
          </q-item-label>
          <q-item-label caption>
            {{ Math.round(budget.percentage || 0) }}% used
          </q-item-label>
        </q-item-section>
        
        <q-item-section side v-if="showActions">
          <div class="row">
            <q-btn
              flat
              round
              dense
              color="primary"
              icon="edit"
              @click.stop="$emit('edit', budget.id)"
            />
            <q-btn
              flat
              round
              dense
              color="negative"
              icon="delete"
              @click.stop="$emit('delete', budget.id)"
            />
          </div>
        </q-item-section>
      </q-item>
    </q-list>
    
    <div v-if="budgets.length === 0" class="text-center q-pa-md">
      <q-icon name="account_balance_wallet" size="3rem" color="grey-5" />
      <div class="text-subtitle1 q-mt-sm">No budgets found</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import { date } from 'quasar';
import { Budget } from 'src/services/budget.service';

const props = defineProps({
  budgets: {
    type: Array as () => Budget[],
    required: true
  },
  showActions: {
    type: Boolean,
    default: true
  }
});

defineEmits(['select', 'edit', 'delete']);

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Format date range
const formatDateRange = (startDate: string, endDate: string) => {
  const start = date.formatDate(startDate, 'MMM D, YYYY');
  const end = date.formatDate(endDate, 'MMM D, YYYY');
  return `${start} - ${end}`;
};

// Get budget progress
const getBudgetProgress = (budget: Budget) => {
  if (budget.amount <= 0) return 0;
  const progress = (budget.spent_amount || 0) / budget.amount;
  return Math.min(progress, 1); // Cap at 100%
};

// Get budget color
const getBudgetColor = (budget: Budget) => {
  const progress = getBudgetProgress(budget);
  if (progress >= 0.9) return 'negative';
  if (progress >= 0.7) return 'warning';
  return 'positive';
};
</script>
