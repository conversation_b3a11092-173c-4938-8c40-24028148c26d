<template>
  <base-form
    ref="formRef"
    :loading="loading"
    :submit-label="submitLabel"
    @submit="onSubmit"
    @reset="onReset"
  >
    <div class="row q-col-gutter-md">
      <div class="col-12 col-md-6">
        <q-select
          v-model="form.category_id"
          :options="categoryOptions"
          label="Category"
          :rules="[val => !!val || 'Category is required']"
          lazy-rules
          emit-value
          map-options
          option-label="label"
          option-value="value"
        >
          <template v-slot:prepend>
            <q-icon name="category" />
          </template>
        </q-select>
      </div>

      <div class="col-12 col-md-6">
        <q-select
          v-model="form.period"
          :options="periodOptions"
          label="Period"
          :rules="[val => !!val || 'Period is required']"
          lazy-rules
        >
          <template v-slot:prepend>
            <q-icon name="date_range" />
          </template>
        </q-select>
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model="form.start_date"
          label="Start Date"
          :rules="[val => !!val || 'Start date is required']"
          lazy-rules
        >
          <template v-slot:prepend>
            <q-icon name="event" />
          </template>
          <template v-slot:append>
            <q-icon name="event" class="cursor-pointer">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-date v-model="form.start_date" mask="YYYY-MM-DD" />
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>
      </div>

      <div class="col-12 col-md-6">
        <q-input
          v-model="form.end_date"
          label="End Date"
          :rules="[
            val => !!val || 'End date is required',
            val => isValidDateRange(form.start_date, val) || 'End date must be after start date'
          ]"
          lazy-rules
        >
          <template v-slot:prepend>
            <q-icon name="event" />
          </template>
          <template v-slot:append>
            <q-icon name="event" class="cursor-pointer">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-date v-model="form.end_date" mask="YYYY-MM-DD" />
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>
      </div>

      <div class="col-12">
        <q-input
          v-model.number="form.amount"
          label="Budget Amount"
          type="number"
          step="0.01"
          :rules="[val => val > 0 || 'Amount must be greater than 0']"
          lazy-rules
        >
          <template v-slot:prepend>
            <q-icon name="attach_money" />
          </template>
        </q-input>
      </div>

      <div class="col-12">
        <q-input
          v-model="form.notes"
          label="Notes"
          type="textarea"
          autogrow
        >
          <template v-slot:prepend>
            <q-icon name="notes" />
          </template>
        </q-input>
      </div>
    </div>
  </base-form>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, onMounted, computed } from 'vue';
import { date } from 'quasar';
import BaseForm from 'src/components/common/BaseForm.vue';
import type { Budget, BudgetRequest } from 'src/services/budget.service';
import { useCategoryStore } from 'src/stores/category.store';

const props = defineProps({
  budget: {
    type: Object as () => Budget | null,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'create',
    validator: (value: string) => ['create', 'edit'].includes(value)
  }
});

const emit = defineEmits(['submit', 'reset']);

const formRef = ref<InstanceType<typeof BaseForm> | null>(null);
const categoryStore = useCategoryStore();

// Budget periods
const periodOptions = [
  'monthly',
  'quarterly',
  'yearly',
  'custom'
];

// Form data
const form = ref<BudgetRequest>({
  category_id: 0,
  amount: 0,
  period: 'monthly',
  start_date: date.formatDate(new Date(), 'YYYY-MM-DD'),
  end_date: date.formatDate(date.addToDate(new Date(), { months: 1 }), 'YYYY-MM-DD'),
  notes: ''
});

// Category options
const categoryOptions = computed(() => {
  return categoryStore.expenseCategories.map(category => ({
    label: category.name,
    value: category.id
  }));
});

// Submit label
const submitLabel = computed(() => {
  return props.mode === 'create' ? 'Create Budget' : 'Update Budget';
});

// Initialize form with budget data if in edit mode
watch(() => props.budget, (newBudget) => {
  if (newBudget) {
    form.value = {
      category_id: newBudget.category_id,
      amount: newBudget.amount,
      period: newBudget.period,
      start_date: newBudget.start_date.substring(0, 10), // Extract YYYY-MM-DD
      end_date: newBudget.end_date.substring(0, 10), // Extract YYYY-MM-DD
      notes: newBudget.notes || ''
    };
  }
}, { immediate: true });

// Watch period to update date range
watch(() => form.value.period, (newPeriod) => {
  if (newPeriod === 'custom') return;

  const now = new Date();
  let startDate = new Date(now);
  let endDate = new Date(now);

  // Set start date to beginning of current period
  switch (newPeriod) {
    case 'monthly':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      break;
    case 'quarterly': {
      const quarter = Math.floor(now.getMonth() / 3);
      startDate = new Date(now.getFullYear(), quarter * 3, 1);
      endDate = new Date(now.getFullYear(), (quarter + 1) * 3, 0);
      break;
    }
    case 'yearly':
      startDate = new Date(now.getFullYear(), 0, 1);
      endDate = new Date(now.getFullYear(), 11, 31);
      break;
  }

  form.value.start_date = date.formatDate(startDate, 'YYYY-MM-DD');
  form.value.end_date = date.formatDate(endDate, 'YYYY-MM-DD');
});

// Validate date range
const isValidDateRange = (startDate: string, endDate: string) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  return end >= start;
};

// Submit form
const onSubmit = () => {
  emit('submit', { ...form.value });
};

// Reset form
const onReset = () => {
  if (props.budget) {
    form.value = {
      category_id: props.budget.category_id,
      amount: props.budget.amount,
      period: props.budget.period,
      start_date: props.budget.start_date.substring(0, 10),
      end_date: props.budget.end_date.substring(0, 10),
      notes: props.budget.notes || ''
    };
  } else {
    form.value = {
      category_id: 0,
      amount: 0,
      period: 'monthly',
      start_date: date.formatDate(new Date(), 'YYYY-MM-DD'),
      end_date: date.formatDate(date.addToDate(new Date(), { months: 1 }), 'YYYY-MM-DD'),
      notes: ''
    };
  }

  emit('reset');
};

// Load categories on component mount
onMounted(async () => {
  if (categoryStore.categories.length === 0) {
    await categoryStore.fetchCategories();
  }
});

// Expose methods to parent component
defineExpose({
  validate: async () => await formRef.value?.validate(),
  reset: () => formRef.value?.reset()
});
</script>
