package service

import (
	"errors"

	"gorm.io/gorm"

	"github.com/imaojia/easycash/internal/model"
)

// AccountService handles account related operations
type AccountService struct {
	db *gorm.DB
}

// NewAccountService creates a new account service
func NewAccountService(db *gorm.DB) *AccountService {
	return &AccountService{db: db}
}

// CreateAccount creates a new account for a user
func (s *AccountService) CreateAccount(userID uint, req model.AccountRequest) (*model.AccountResponse, error) {
	// Check if this is the first account for the user
	var count int64
	if err := s.db.Model(&model.Account{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return nil, err
	}

	// If this is the first account, make it default
	isDefault := req.IsDefault
	if count == 0 {
		isDefault = true
	}

	// Create new account
	account := model.Account{
		UserID:    userID,
		Name:      req.Name,
		Type:      req.Type,
		Balance:   req.Balance,
		Currency:  req.Currency,
		Icon:      req.Icon,
		Color:     req.Color,
		IsDefault: isDefault,
	}

	// If this account is set as default, unset other default accounts
	if isDefault {
		if err := s.db.Model(&model.Account{}).Where("user_id = ?", userID).Update("is_default", false).Error; err != nil {
			return nil, err
		}
	}

	// Save the account
	if err := s.db.Create(&account).Error; err != nil {
		return nil, err
	}

	return &model.AccountResponse{
		ID:        account.ID,
		UserID:    account.UserID,
		Name:      account.Name,
		Type:      account.Type,
		Balance:   account.Balance,
		Currency:  account.Currency,
		Icon:      account.Icon,
		Color:     account.Color,
		IsDefault: account.IsDefault,
		CreatedAt: account.CreatedAt,
		UpdatedAt: account.UpdatedAt,
	}, nil
}

// GetAccountByID retrieves an account by ID
func (s *AccountService) GetAccountByID(userID, accountID uint) (*model.Account, error) {
	var account model.Account
	if err := s.db.Where("id = ? AND user_id = ?", accountID, userID).First(&account).Error; err != nil {
		return nil, err
	}
	return &account, nil
}

// UpdateAccount updates an account
func (s *AccountService) UpdateAccount(userID, accountID uint, req model.AccountRequest) (*model.AccountResponse, error) {
	// Get the account
	account, err := s.GetAccountByID(userID, accountID)
	if err != nil {
		return nil, err
	}

	// Update fields
	account.Name = req.Name
	account.Type = req.Type
	account.Balance = req.Balance
	account.Currency = req.Currency
	account.Icon = req.Icon
	account.Color = req.Color

	// Handle default account status
	if req.IsDefault && !account.IsDefault {
		// If setting this account as default, unset other default accounts
		if err := s.db.Model(&model.Account{}).Where("user_id = ?", userID).Update("is_default", false).Error; err != nil {
			return nil, err
		}
		account.IsDefault = true
	} else if !req.IsDefault && account.IsDefault {
		// Check if this is the only account
		var count int64
		if err := s.db.Model(&model.Account{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
			return nil, err
		}

		// Don't allow unsetting default if this is the only account
		if count == 1 {
			return nil, errors.New("cannot unset default status for the only account")
		}

		account.IsDefault = false
	}

	// Save the account
	if err := s.db.Save(account).Error; err != nil {
		return nil, err
	}

	return &model.AccountResponse{
		ID:        account.ID,
		UserID:    account.UserID,
		Name:      account.Name,
		Type:      account.Type,
		Balance:   account.Balance,
		Currency:  account.Currency,
		Icon:      account.Icon,
		Color:     account.Color,
		IsDefault: account.IsDefault,
		CreatedAt: account.CreatedAt,
		UpdatedAt: account.UpdatedAt,
	}, nil
}

// DeleteAccount deletes an account
func (s *AccountService) DeleteAccount(userID, accountID uint) error {
	// Get the account
	account, err := s.GetAccountByID(userID, accountID)
	if err != nil {
		return err
	}

	// Check if this is the default account
	if account.IsDefault {
		// Count total accounts
		var count int64
		if err := s.db.Model(&model.Account{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
			return err
		}

		// If there are other accounts, set another one as default
		if count > 1 {
			var newDefault model.Account
			if err := s.db.Where("user_id = ? AND id != ?", userID, accountID).First(&newDefault).Error; err != nil {
				return err
			}

			newDefault.IsDefault = true
			if err := s.db.Save(&newDefault).Error; err != nil {
				return err
			}
		}
	}

	// Delete the account
	return s.db.Delete(account).Error
}

// ListAccounts retrieves accounts for a user
func (s *AccountService) ListAccounts(params model.AccountListParams) ([]model.Account, int64, error) {
	var accounts []model.Account
	var total int64

	// Build query
	query := s.db.Model(&model.Account{}).Where("user_id = ?", params.UserID)

	// Apply filters
	if params.Type != "" {
		query = query.Where("type = ?", params.Type)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (params.Page - 1) * params.Limit
	if err := query.Offset(offset).Limit(params.Limit).Find(&accounts).Error; err != nil {
		return nil, 0, err
	}

	return accounts, total, nil
}

// GetDefaultAccount retrieves the default account for a user
func (s *AccountService) GetDefaultAccount(userID uint) (*model.Account, error) {
	var account model.Account
	if err := s.db.Where("user_id = ? AND is_default = ?", userID, true).First(&account).Error; err != nil {
		return nil, err
	}
	return &account, nil
}

// UpdateAccountBalance updates an account's balance
func (s *AccountService) UpdateAccountBalance(accountID uint, amount float64) error {
	return s.db.Model(&model.Account{}).Where("id = ?", accountID).
		UpdateColumn("balance", gorm.Expr("balance + ?", amount)).Error
}
