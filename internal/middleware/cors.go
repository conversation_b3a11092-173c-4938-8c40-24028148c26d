package middleware

import (
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

// CORS returns a middleware for handling CORS
func CORS() gin.HandlerFunc {
	// Get allowed origins from config
	allowedOrigins := viper.GetStringSlice("app.cors.allowed_origins")
	if len(allowedOrigins) == 0 {
		allowedOrigins = []string{"*"}
	}

	// Get allowed methods from config
	allowedMethods := viper.GetStringSlice("app.cors.allowed_methods")
	if len(allowedMethods) == 0 {
		allowedMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"}
	}

	// Get allowed headers from config
	allowedHeaders := viper.GetStringSlice("app.cors.allowed_headers")
	if len(allowedHeaders) == 0 {
		allowedHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization"}
	}

	// Get exposed headers from config
	exposedHeaders := viper.GetStringSlice("app.cors.exposed_headers")
	if len(exposedHeaders) == 0 {
		exposedHeaders = []string{"Content-Length"}
	}

	// Get max age from config
	maxAge := viper.GetInt("app.cors.max_age")
	if maxAge == 0 {
		maxAge = 12 * 60 * 60 // 12 hours
	}

	// Configure CORS
	config := cors.Config{
		AllowOrigins:     allowedOrigins,
		AllowMethods:     allowedMethods,
		AllowHeaders:     allowedHeaders,
		ExposeHeaders:    exposedHeaders,
		AllowCredentials: true,
		MaxAge:           time.Duration(maxAge) * time.Second,
	}

	return cors.New(config)
}
