# EasyCash 开发规划文档

参考:  
https://github.com/wilinz/easy_write
https://github.com/wilinz/easywrite_service

## 一、项目概述

EasyCash 是一款现代化的个人财务管理应用，旨在提供简单、智能的记账体验。本项目采用前后端分离架构，前端使用 Vue.js + TypeScript，后端使用 Go 语言开发。

## 二、技术栈选型

### 前端技术栈

- 框架：Vue 3 + TypeScript
- UI 框架：Quasar v2
- 状态管理：Pinia
- 路由：Vue Router
- HTTP 客户端：Axios
- 图表库：VChart
- 本地存储：IndexedDB
- 构建工具：Vite

### 后端技术栈

- 语言：Go
- Web 框架：Gin
- 数据库：PostgreSQL
- ORM：GORM
- 缓存：Redis
- 消息队列：NSQ（轻量级分布式消息队列）
- 对象存储：MinIO
- 认证：JWT

## 三、开发阶段规划

### 第一阶段：基础架构搭建（2 周）

#### 前端

1. 项目初始化

   - 搭建 Vue 3 + TypeScript 项目
   - 配置 Quasar 框架
   - 设置开发环境
   - 配置 ESLint + Prettier

2. 基础组件开发
   - 布局组件
   - 导航组件
   - 表单组件
   - 列表组件
   - 图表组件

#### 后端

1. 项目初始化

   - 搭建 Go 项目结构
   - 配置 Gin 框架
   - 设置数据库连接
   - 配置日志系统

2. 基础服务开发
   - 用户认证服务
   - 数据库迁移
   - API 文档生成
   - 错误处理中间件

### 第二阶段：核心功能开发（4 周）

#### 前端

1. 记账功能

   - 快速记录界面
   - 分类管理
   - 账户管理
   - 预算设置

2. 数据展示
   - 消费趋势图
   - 分类占比图
   - 预算进度展示
   - 数据筛选功能

#### 后端

1. 核心 API 开发

   - 用户管理 API
   - 记账记录 API
   - 分类管理 API
   - 账户管理 API
   - 预算管理 API

2. 数据处理服务
   - 数据统计服务
   - 预算计算服务
   - 数据导出服务

### 第三阶段：高级功能开发（3 周）

#### 前端

1. 智能功能

   - OCR 识别集成
   - 语音输入集成
   - 智能分类预测
   - 自动填充功能

2. 数据同步
   - 本地数据存储
   - 云端同步
   - 数据备份恢复

#### 后端

1. 高级服务开发

   - OCR 服务集成
   - 语音识别服务
   - 智能分析服务
   - 数据同步服务

2. 安全功能
   - 数据加密
   - 访问控制
   - 审计日志

### 第四阶段：优化和测试（2 周）

#### 前端

1. 性能优化

   - 代码分割
   - 懒加载
   - 缓存优化
   - 渲染优化

2. 测试
   - 单元测试
   - 集成测试
   - E2E 测试
   - 性能测试

#### 后端

1. 性能优化

   - 数据库优化
   - 缓存优化
   - 并发处理
   - 资源管理

2. 测试
   - 单元测试
   - 接口测试
   - 压力测试
   - 安全测试

## 四、数据库设计

### 用户表 (users)

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 账户表 (accounts)

```sql
CREATE TABLE accounts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    name VARCHAR(50) NOT NULL,
    type VARCHAR(20) NOT NULL,
    balance DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 分类表 (categories)

```sql
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    name VARCHAR(50) NOT NULL,
    type VARCHAR(20) NOT NULL,
    icon VARCHAR(50),
    parent_id INTEGER REFERENCES categories(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 交易记录表 (transactions)

```sql
CREATE TABLE transactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    account_id INTEGER REFERENCES accounts(id),
    category_id INTEGER REFERENCES categories(id),
    amount DECIMAL(10,2) NOT NULL,
    type VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 预算表 (budgets)

```sql
CREATE TABLE budgets (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    category_id INTEGER REFERENCES categories(id),
    amount DECIMAL(10,2) NOT NULL,
    period VARCHAR(20) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 五、API 接口设计

### 用户认证

- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录
- POST /api/auth/logout - 用户登出
- POST /api/auth/refresh - 刷新令牌

### 账户管理

- GET /api/accounts - 获取账户列表
- POST /api/accounts - 创建新账户
- PUT /api/accounts/:id - 更新账户
- DELETE /api/accounts/:id - 删除账户

### 分类管理

- GET /api/categories - 获取分类列表
- POST /api/categories - 创建新分类
- PUT /api/categories/:id - 更新分类
- DELETE /api/categories/:id - 删除分类

### 交易记录

- GET /api/transactions - 获取交易记录
- POST /api/transactions - 创建新交易
- PUT /api/transactions/:id - 更新交易
- DELETE /api/transactions/:id - 删除交易

### 预算管理

- GET /api/budgets - 获取预算列表
- POST /api/budgets - 创建新预算
- PUT /api/budgets/:id - 更新预算
- DELETE /api/budgets/:id - 删除预算

### 数据统计

- GET /api/statistics/daily - 每日统计
- GET /api/statistics/monthly - 每月统计
- GET /api/statistics/yearly - 每年统计
- GET /api/statistics/category - 分类统计

## 六、部署方案

### 开发环境

- 前端：Vite 开发服务器
- 后端：Go 开发服务器
- 数据库：PostgreSQL 本地实例
- 缓存：Redis 本地实例

### 生产环境

- 前端：Nginx 静态文件服务
- 后端：Docker 容器化部署
- 数据库：PostgreSQL 主从复制
- 缓存：Redis 集群
- 对象存储：MinIO 集群
- 监控：Prometheus + Grafana

## 七、项目进度管理

### 里程碑

1. M1：基础架构完成（2 周）
2. M2：核心功能完成（4 周）
3. M3：高级功能完成（3 周）
4. M4：测试和优化完成（2 周）

### 每日任务

- 晨会：讨论当日任务和问题
- 代码审查：确保代码质量
- 进度更新：更新任务状态
- 问题跟踪：记录和解决技术问题

## 八、风险管理

### 技术风险

1. 性能问题

   - 解决方案：定期性能测试和优化
   - 责任人：技术负责人

2. 数据安全

   - 解决方案：实施严格的安全措施
   - 责任人：安全负责人

3. 第三方服务依赖
   - 解决方案：准备备用方案
   - 责任人：架构师

### 项目风险

1. 进度延迟

   - 解决方案：合理规划缓冲时间
   - 责任人：项目经理

2. 需求变更
   - 解决方案：严格控制变更流程
   - 责任人：产品经理

## 九、质量保证

### 代码质量

- 代码审查
- 单元测试覆盖率 > 80%
- 静态代码分析
- 持续集成

### 测试策略

1. 单元测试

   - 前端：Jest + Vue Test Utils
   - 后端：Go 标准测试库

2. 集成测试

   - API 测试
   - 数据库测试
   - 缓存测试

3. 端到端测试
   - Cypress
   - 场景测试
   - 性能测试

## 十、文档管理

### 技术文档

- API 文档
- 数据库设计文档
- 部署文档
- 开发环境配置文档

### 用户文档

- 用户手册
- 功能说明
- 常见问题解答
- 更新日志
