package main

import (
	"fmt"
	"log"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/imaojia/easycash/internal/middleware"
	"github.com/imaojia/easycash/internal/model"
	"github.com/imaojia/easycash/internal/route"
)

func main() {
	// Load configuration
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("configs")
	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("Error reading config file: %s", err)
	}

	// Initialize database connection
	dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s TimeZone=%s",
		viper.GetString("database.host"),
		viper.GetInt("database.port"),
		viper.GetString("database.user"),
		viper.GetString("database.password"),
		viper.GetString("database.dbname"),
		viper.GetString("database.sslmode"),
		viper.GetString("database.timezone"),
	)

	// Configure GORM
	gormConfig := &gorm.Config{}
	if viper.GetString("app.mode") == "debug" {
		gormConfig.Logger = logger.Default.LogMode(logger.Info)
	}

	db, err := gorm.Open(postgres.Open(dsn), gormConfig)
	if err != nil {
		log.Fatalf("Failed to connect to database: %s", err)
	}

	// Auto migrate database models
	if err := db.AutoMigrate(
		&model.User{},
		&model.Account{},
		&model.Category{},
		&model.Transaction{},
		&model.Budget{},
	); err != nil {
		log.Fatalf("Failed to migrate database: %s", err)
	}

	// Set Gin mode
	gin.SetMode(viper.GetString("app.mode"))

	// Create Gin engine
	r := gin.Default()

	// Configure CORS
	r.Use(middleware.CORS())

	// Configure validator
	r.Use(middleware.Validator())

	// Setup routes
	route.SetupRoutes(r, db)

	// Start server
	addr := fmt.Sprintf(":%d", viper.GetInt("app.port"))
	log.Printf("Server starting on %s", addr)
	if err := r.Run(addr); err != nil {
		log.Fatalf("Failed to start server: %s", err)
	}
}
