import { defineStore } from 'pinia';
import { ref } from 'vue';
import { api } from 'src/boot/axios';

export interface User {
  id: number;
  username: string;
  email: string;
  avatar?: string;
  preferences: {
    currency: string;
    theme: 'light' | 'dark' | 'system';
    language: string;
  };
  createdAt: string;
  updatedAt: string;
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const profile = ref<User | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Actions
  const fetchProfile = async () => {
    loading.value = true;
    error.value = null;

    try {
      const response = await api.get('/users/profile');
      profile.value = response.data;
      return profile.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取用户信息失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updateProfile = async (data: Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt'>>) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await api.put('/users/profile', data);
      profile.value = response.data;
      return profile.value;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新用户信息失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const updatePreferences = async (preferences: Partial<User['preferences']>) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await api.put('/users/preferences', preferences);
      if (profile.value) {
        profile.value.preferences = {
          ...profile.value.preferences,
          ...response.data
        };
      }
      return profile.value?.preferences;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新用户偏好设置失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const uploadAvatar = async (file: File) => {
    loading.value = true;
    error.value = null;

    try {
      const formData = new FormData();
      formData.append('avatar', file);
      
      const response = await api.post('/users/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      if (profile.value) {
        profile.value.avatar = response.data.avatar;
      }
      
      return response.data.avatar;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '上传头像失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    profile,
    loading,
    error,
    fetchProfile,
    updateProfile,
    updatePreferences,
    uploadAvatar
  };
}); 