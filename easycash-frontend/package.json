{"name": "easycash-frontend", "version": "0.0.1", "description": "easy cash app", "productName": "Easy Cash", "author": "<PERSON> <<EMAIL>>", "type": "module", "private": true, "scripts": {"lint": "eslint -c ./eslint.config.js \"./src*/**/*.{ts,js,cjs,mjs,vue}\"", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build", "postinstall": "quasar prepare"}, "dependencies": {"@quasar/extras": "^1.16.4", "@upstash/context7-mcp": "^1.0.6", "axios": "^1.2.1", "pinia": "^3.0.1", "quasar": "^2.16.0", "vue": "^3.4.18", "vue-i18n": "^11.0.0", "vue-router": "^4.0.12"}, "devDependencies": {"@eslint/js": "^9.14.0", "@intlify/unplugin-vue-i18n": "^4.0.0", "@quasar/app-vite": "^2.1.0", "@types/node": "^20.5.9", "@vue/eslint-config-typescript": "^14.4.0", "autoprefixer": "^10.4.2", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "globals": "^15.12.0", "typescript": "~5.5.3", "vite-plugin-checker": "^0.9.0", "vue-tsc": "^2.0.29"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}