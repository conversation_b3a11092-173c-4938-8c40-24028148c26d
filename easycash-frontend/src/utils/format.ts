import { date } from 'quasar';

/**
 * Format date to readable string
 */
export function formatDate(dateStr: string | Date): string {
  if (!dateStr) return '';
  
  const dateObj = typeof dateStr === 'string' ? new Date(dateStr) : dateStr;
  return date.formatDate(dateObj, 'YYYY-MM-DD');
}

/**
 * Format date and time to readable string
 */
export function formatDateTime(dateStr: string | Date): string {
  if (!dateStr) return '';
  
  const dateObj = typeof dateStr === 'string' ? new Date(dateStr) : dateStr;
  return date.formatDate(dateObj, 'YYYY-MM-DD HH:mm:ss');
}

/**
 * Format date header for grouping
 */
export function formatDateHeader(dateStr: string | Date): string {
  if (!dateStr) return '';
  
  const dateObj = typeof dateStr === 'string' ? new Date(dateStr) : dateStr;
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  if (date.isSameDate(dateObj, today, 'day')) {
    return '今天';
  } else if (date.isSameDate(dateObj, yesterday, 'day')) {
    return '昨天';
  } else {
    return date.formatDate(dateObj, 'MM月DD日');
  }
}

/**
 * Format amount with currency symbol
 */
export function formatAmount(amount: number, currency = '¥'): string {
  if (typeof amount !== 'number') return '¥0.00';
  
  return `${currency}${amount.toFixed(2)}`;
}

/**
 * Format amount with color class based on type
 */
export function formatAmountWithColor(amount: number, type: 'income' | 'expense' | 'transfer'): { text: string; class: string } {
  const formattedAmount = formatAmount(Math.abs(amount));
  
  switch (type) {
    case 'income':
      return {
        text: `+${formattedAmount}`,
        class: 'text-positive'
      };
    case 'expense':
      return {
        text: `-${formattedAmount}`,
        class: 'text-negative'
      };
    case 'transfer':
      return {
        text: formattedAmount,
        class: 'text-info'
      };
    default:
      return {
        text: formattedAmount,
        class: ''
      };
  }
}
