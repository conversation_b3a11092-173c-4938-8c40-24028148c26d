<template>
  <q-page padding>
    <page-header
      :title="mode === 'create' ? 'Create Account' : 'Edit Account'"
      show-back-button
    />
    
    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading && !formLoading" message="Loading account data..." />
    
    <q-card>
      <q-card-section>
        <account-form
          ref="formRef"
          :account="account"
          :loading="formLoading"
          :mode="mode"
          @submit="onSubmit"
          @reset="onReset"
        />
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import AccountForm from 'src/components/accounts/AccountForm.vue';
import { useAccountStore } from 'src/stores/account.store';
import { AccountRequest } from 'src/services/account.service';
import NotificationService from 'src/services/notification.service';

const props = defineProps({
  id: {
    type: Number,
    default: 0
  },
  mode: {
    type: String,
    default: 'create',
    validator: (value: string) => ['create', 'edit'].includes(value)
  }
});

const router = useRouter();
const route = useRoute();
const accountStore = useAccountStore();

const formRef = ref<InstanceType<typeof AccountForm> | null>(null);
const loading = ref(false);
const formLoading = ref(false);
const error = ref<string | null>(null);
const account = ref(accountStore.currentAccount);

// Computed properties
const isEditMode = computed(() => props.mode === 'edit');
const accountId = computed(() => props.id || Number(route.params.id));

// Load account data if in edit mode
const loadAccount = async () => {
  if (!isEditMode.value || !accountId.value) return;
  
  loading.value = true;
  error.value = null;
  
  try {
    await accountStore.fetchAccount(accountId.value);
    account.value = accountStore.currentAccount;
  } catch (err: any) {
    error.value = 'Failed to load account. Please try again.';
    NotificationService.error('Failed to load account');
    console.error('Load account error:', err);
  } finally {
    loading.value = false;
  }
};

// Submit form
const onSubmit = async (formData: AccountRequest) => {
  formLoading.value = true;
  error.value = null;
  
  try {
    if (isEditMode.value && accountId.value) {
      await accountStore.updateAccount(accountId.value, formData);
      NotificationService.success('Account updated successfully');
    } else {
      await accountStore.createAccount(formData);
      NotificationService.success('Account created successfully');
    }
    
    router.push('/accounts');
  } catch (err: any) {
    error.value = isEditMode.value
      ? 'Failed to update account. Please try again.'
      : 'Failed to create account. Please try again.';
    NotificationService.error(error.value);
    console.error('Account form error:', err);
  } finally {
    formLoading.value = false;
  }
};

// Reset form
const onReset = () => {
  error.value = null;
};

// Load account data on component mount
onMounted(() => {
  loadAccount();
});
</script>
