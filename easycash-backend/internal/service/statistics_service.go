package service

import (
	"time"

	"gorm.io/gorm"
)

// StatisticsService handles statistics related operations
type StatisticsService struct {
	db *gorm.DB
}

// NewStatisticsService creates a new statistics service
func NewStatisticsService(db *gorm.DB) *StatisticsService {
	return &StatisticsService{db: db}
}

// DailyStatistics represents daily income and expense statistics
type DailyStatistics struct {
	Date    time.Time `json:"date"`
	Income  float64   `json:"income"`
	Expense float64   `json:"expense"`
	Balance float64   `json:"balance"`
}

// CategoryStatistics represents statistics for a category
type CategoryStatistics struct {
	CategoryID   uint    `json:"category_id"`
	CategoryName string  `json:"category_name"`
	Amount       float64 `json:"amount"`
	Percentage   float64 `json:"percentage"`
}

// AccountStatistics represents statistics for an account
type AccountStatistics struct {
	AccountID   uint    `json:"account_id"`
	AccountName string  `json:"account_name"`
	Balance     float64 `json:"balance"`
	Percentage  float64 `json:"percentage"`
}

// MonthlyStatistics represents monthly income and expense statistics
type MonthlyStatistics struct {
	Year    int     `json:"year"`
	Month   int     `json:"month"`
	Income  float64 `json:"income"`
	Expense float64 `json:"expense"`
	Balance float64 `json:"balance"`
}

// YearlyStatistics represents yearly income and expense statistics
type YearlyStatistics struct {
	Year    int     `json:"year"`
	Income  float64 `json:"income"`
	Expense float64 `json:"expense"`
	Balance float64 `json:"balance"`
}

// GetDailyStatistics retrieves daily statistics for a user within a date range
func (s *StatisticsService) GetDailyStatistics(userID uint, startDate, endDate time.Time) ([]DailyStatistics, error) {
	var results []struct {
		Date    time.Time `json:"date"`
		Type    string    `json:"type"`
		Amount  float64   `json:"amount"`
	}

	// Query daily totals by type
	err := s.db.Model(&struct{}{}).
		Table("transactions").
		Select("date, type, SUM(amount) as amount").
		Where("user_id = ? AND date BETWEEN ? AND ? AND type IN ('income', 'expense')", userID, startDate, endDate).
		Group("date, type").
		Order("date").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	// Organize results by date
	dateMap := make(map[time.Time]DailyStatistics)
	for _, result := range results {
		date := time.Date(result.Date.Year(), result.Date.Month(), result.Date.Day(), 0, 0, 0, 0, time.UTC)
		stats, exists := dateMap[date]
		if !exists {
			stats = DailyStatistics{Date: date}
		}

		if result.Type == "income" {
			stats.Income = result.Amount
		} else if result.Type == "expense" {
			stats.Expense = result.Amount
		}

		stats.Balance = stats.Income - stats.Expense
		dateMap[date] = stats
	}

	// Fill in missing dates in the range
	var statistics []DailyStatistics
	for d := startDate; !d.After(endDate); d = d.AddDate(0, 0, 1) {
		date := time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, time.UTC)
		stats, exists := dateMap[date]
		if !exists {
			stats = DailyStatistics{Date: date}
		}
		statistics = append(statistics, stats)
	}

	return statistics, nil
}

// GetMonthlyStatistics retrieves monthly statistics for a user within a date range
func (s *StatisticsService) GetMonthlyStatistics(userID uint, startDate, endDate time.Time) ([]MonthlyStatistics, error) {
	var results []struct {
		Year    int     `json:"year"`
		Month   int     `json:"month"`
		Type    string  `json:"type"`
		Amount  float64 `json:"amount"`
	}

	// Query monthly totals by type
	err := s.db.Model(&struct{}{}).
		Table("transactions").
		Select("EXTRACT(YEAR FROM date) as year, EXTRACT(MONTH FROM date) as month, type, SUM(amount) as amount").
		Where("user_id = ? AND date BETWEEN ? AND ? AND type IN ('income', 'expense')", userID, startDate, endDate).
		Group("year, month, type").
		Order("year, month").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	// Organize results by year and month
	monthMap := make(map[string]MonthlyStatistics)
	for _, result := range results {
		key := time.Date(result.Year, time.Month(result.Month), 1, 0, 0, 0, 0, time.UTC).Format("2006-01")
		stats, exists := monthMap[key]
		if !exists {
			stats = MonthlyStatistics{Year: result.Year, Month: result.Month}
		}

		if result.Type == "income" {
			stats.Income = result.Amount
		} else if result.Type == "expense" {
			stats.Expense = result.Amount
		}

		stats.Balance = stats.Income - stats.Expense
		monthMap[key] = stats
	}

	// Fill in missing months in the range
	var statistics []MonthlyStatistics
	startMonth := time.Date(startDate.Year(), startDate.Month(), 1, 0, 0, 0, 0, time.UTC)
	endMonth := time.Date(endDate.Year(), endDate.Month(), 1, 0, 0, 0, 0, time.UTC)

	for d := startMonth; !d.After(endMonth); d = d.AddDate(0, 1, 0) {
		key := d.Format("2006-01")
		stats, exists := monthMap[key]
		if !exists {
			stats = MonthlyStatistics{Year: d.Year(), Month: int(d.Month())}
		}
		statistics = append(statistics, stats)
	}

	return statistics, nil
}

// GetYearlyStatistics retrieves yearly statistics for a user within a date range
func (s *StatisticsService) GetYearlyStatistics(userID uint, startYear, endYear int) ([]YearlyStatistics, error) {
	var results []struct {
		Year    int     `json:"year"`
		Type    string  `json:"type"`
		Amount  float64 `json:"amount"`
	}

	// Query yearly totals by type
	err := s.db.Model(&struct{}{}).
		Table("transactions").
		Select("EXTRACT(YEAR FROM date) as year, type, SUM(amount) as amount").
		Where("user_id = ? AND EXTRACT(YEAR FROM date) BETWEEN ? AND ? AND type IN ('income', 'expense')", userID, startYear, endYear).
		Group("year, type").
		Order("year").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	// Organize results by year
	yearMap := make(map[int]YearlyStatistics)
	for _, result := range results {
		stats, exists := yearMap[result.Year]
		if !exists {
			stats = YearlyStatistics{Year: result.Year}
		}

		if result.Type == "income" {
			stats.Income = result.Amount
		} else if result.Type == "expense" {
			stats.Expense = result.Amount
		}

		stats.Balance = stats.Income - stats.Expense
		yearMap[result.Year] = stats
	}

	// Fill in missing years in the range
	var statistics []YearlyStatistics
	for year := startYear; year <= endYear; year++ {
		stats, exists := yearMap[year]
		if !exists {
			stats = YearlyStatistics{Year: year}
		}
		statistics = append(statistics, stats)
	}

	return statistics, nil
}

// GetCategoryStatistics retrieves category statistics for a user within a date range
func (s *StatisticsService) GetCategoryStatistics(userID uint, transactionType string, startDate, endDate time.Time) ([]CategoryStatistics, error) {
	var results []struct {
		CategoryID   uint    `json:"category_id"`
		CategoryName string  `json:"category_name"`
		Amount       float64 `json:"amount"`
	}

	// Query category totals
	query := s.db.Model(&struct{}{}).
		Table("transactions t").
		Select("t.category_id, c.name as category_name, SUM(t.amount) as amount").
		Joins("JOIN categories c ON t.category_id = c.id").
		Where("t.user_id = ? AND t.date BETWEEN ? AND ?", userID, startDate, endDate).
		Group("t.category_id, c.name").
		Order("amount DESC")

	if transactionType != "" {
		query = query.Where("t.type = ?", transactionType)
	}

	err := query.Scan(&results).Error
	if err != nil {
		return nil, err
	}

	// Calculate total amount for percentage
	var totalAmount float64
	for _, result := range results {
		totalAmount += result.Amount
	}

	// Calculate percentages
	var statistics []CategoryStatistics
	for _, result := range results {
		percentage := 0.0
		if totalAmount > 0 {
			percentage = (result.Amount / totalAmount) * 100
		}

		statistics = append(statistics, CategoryStatistics{
			CategoryID:   result.CategoryID,
			CategoryName: result.CategoryName,
			Amount:       result.Amount,
			Percentage:   percentage,
		})
	}

	return statistics, nil
}

// GetAccountStatistics retrieves account statistics for a user
func (s *StatisticsService) GetAccountStatistics(userID uint) ([]AccountStatistics, error) {
	var results []struct {
		AccountID   uint    `json:"account_id"`
		AccountName string  `json:"account_name"`
		Balance     float64 `json:"balance"`
	}

	// Query account balances
	err := s.db.Model(&struct{}{}).
		Table("accounts").
		Select("id as account_id, name as account_name, balance").
		Where("user_id = ?", userID).
		Order("balance DESC").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	// Calculate total balance for percentage
	var totalBalance float64
	for _, result := range results {
		if result.Balance > 0 {
			totalBalance += result.Balance
		}
	}

	// Calculate percentages
	var statistics []AccountStatistics
	for _, result := range results {
		percentage := 0.0
		if totalBalance > 0 && result.Balance > 0 {
			percentage = (result.Balance / totalBalance) * 100
		}

		statistics = append(statistics, AccountStatistics{
			AccountID:   result.AccountID,
			AccountName: result.AccountName,
			Balance:     result.Balance,
			Percentage:  percentage,
		})
	}

	return statistics, nil
}
