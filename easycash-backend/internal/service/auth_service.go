package service

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/spf13/viper"
	"gorm.io/gorm"

	"github.com/imaojia/easycash/internal/middleware"
	"github.com/imaojia/easycash/internal/model"
)

// AuthService handles authentication related operations
type AuthService struct {
	db *gorm.DB
}

// NewAuthService creates a new auth service
func NewAuthService(db *gorm.DB) *AuthService {
	return &AuthService{db: db}
}

// Register creates a new user
func (s *AuthService) Register(req model.RegisterRequest) (*model.UserResponse, error) {
	// Check if user with the same email already exists
	var existingUser model.User
	if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, errors.New("user with this email already exists")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// Check if user with the same username already exists
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, errors.New("user with this username already exists")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// Create new user
	user := model.User{
		Username: req.Username,
		Email:    req.Email,
		Password: req.Password,
	}

	if err := s.db.Create(&user).Error; err != nil {
		return nil, err
	}

	// Generate token
	token, err := s.GenerateToken(user.ID)
	if err != nil {
		return nil, err
	}

	// Return user response
	return &model.UserResponse{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Avatar:   user.Avatar,
		Token:    token,
	}, nil
}

// Login authenticates a user
func (s *AuthService) Login(req model.LoginRequest) (*model.UserResponse, error) {
	// Find user by email
	var user model.User
	if err := s.db.Where("email = ?", req.Email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid email or password")
		}
		return nil, err
	}

	// Verify password
	if err := user.ComparePassword(req.Password); err != nil {
		return nil, errors.New("invalid email or password")
	}

	// Generate token
	token, err := s.GenerateToken(user.ID)
	if err != nil {
		return nil, err
	}

	// Return user response
	return &model.UserResponse{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Avatar:   user.Avatar,
		Token:    token,
	}, nil
}

// GenerateToken creates a new JWT token for a user
func (s *AuthService) GenerateToken(userID uint) (string, error) {
	// Get JWT settings from config
	secret := viper.GetString("app.jwt.secret")
	expireStr := viper.GetString("app.jwt.expire")
	
	// Parse expiration duration
	expireDuration, err := time.ParseDuration(expireStr)
	if err != nil {
		expireDuration = 24 * time.Hour // Default to 24 hours
	}
	
	// Create claims
	claims := &middleware.Claims{
		UserID: userID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expireDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}
	
	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	
	// Sign token
	return token.SignedString([]byte(secret))
}

// GetUserByID retrieves a user by ID
func (s *AuthService) GetUserByID(userID uint) (*model.User, error) {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// UpdateUser updates a user's information
func (s *AuthService) UpdateUser(userID uint, username, email, avatar string) (*model.UserResponse, error) {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, err
	}
	
	// Update fields
	user.Username = username
	user.Email = email
	if avatar != "" {
		user.Avatar = avatar
	}
	
	if err := s.db.Save(&user).Error; err != nil {
		return nil, err
	}
	
	return &model.UserResponse{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Avatar:   user.Avatar,
	}, nil
}

// ChangePassword changes a user's password
func (s *AuthService) ChangePassword(userID uint, oldPassword, newPassword string) error {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}
	
	// Verify old password
	if err := user.ComparePassword(oldPassword); err != nil {
		return errors.New("incorrect old password")
	}
	
	// Update password
	user.Password = newPassword
	
	// Save user (password will be hashed by BeforeCreate hook)
	return s.db.Save(&user).Error
}
