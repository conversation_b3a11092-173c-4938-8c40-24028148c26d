<template>
  <q-layout view="lHh Lpr lFf">
    <q-page-container class="auth-container">
      <div class="row justify-center items-center full-height">
        <div class="col-md-4 col-sm-8 col-xs-12 q-px-md">
          <q-card class="auth-card">
            <q-card-section class="text-center q-pt-lg">
              <div class="text-h5 text-weight-bold text-primary">Easy Cash</div>
              <div class="text-subtitle2 text-grey-7">Personal Finance Management</div>
            </q-card-section>
            
            <router-view />
            
            <q-card-section class="text-center q-pb-md">
              <div class="text-caption text-grey">
                &copy; {{ new Date().getFullYear() }} EasyCash. All rights reserved.
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
// Auth layout setup
</script>

<style lang="scss">
.auth-container {
  background: linear-gradient(145deg, $primary 0%, $secondary 100%);
  height: 100vh;
  
  .full-height {
    height: 100%;
  }
  
  .auth-card {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
}
</style> 