import ApiService from './api.service'

export interface User {
  id: number
  username: string
  email: string
  avatar?: string
  token?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
}

export interface UpdateProfileRequest {
  username: string
  email: string
  avatar?: string
}

export interface ChangePasswordRequest {
  old_password: string
  new_password: string
}

export const AuthService = {
  // Login user
  login: async (credentials: LoginRequest): Promise<User> => {
    try {
      const user = await ApiService.post<User>('/auth/login', credentials)
      
      // Save token to localStorage
      if (user.token) {
        localStorage.setItem('token', user.token)
      }
      
      return user
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Register user
  register: async (userData: RegisterRequest): Promise<User> => {
    try {
      const user = await ApiService.post<User>('/auth/register', userData)
      
      // Save token to localStorage
      if (user.token) {
        localStorage.setItem('token', user.token)
      }
      
      return user
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Logout user
  logout: (): void => {
    localStorage.removeItem('token')
  },
  
  // Get current user profile
  getProfile: async (): Promise<User> => {
    try {
      return await ApiService.get<User>('/auth/profile')
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Update user profile
  updateProfile: async (profileData: UpdateProfileRequest): Promise<User> => {
    try {
      return await ApiService.put<User>('/auth/profile', profileData)
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Change user password
  changePassword: async (passwordData: ChangePasswordRequest): Promise<void> => {
    try {
      await ApiService.post<void>('/auth/change-password', passwordData)
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem('token')
  }
}

export default AuthService
