<template>
  <q-page padding>
    <!-- Simplified header for testing -->
    <div class="text-h4 q-mb-md">Dashboard</div>
    
    <!-- Simple test content -->
    <div class="text-h6 text-positive q-mb-md">✅ EasyCash Frontend is working!</div>
    
    <!-- Error display (simplified) -->
    <div v-if="error" class="q-pa-md bg-negative text-white rounded-borders q-mb-md">
      {{ error }}
      <q-btn flat dense icon="close" @click="error = null" class="float-right" />
    </div>
    
    <!-- Loading indicator (simplified) -->
    <div v-if="loading" class="text-center q-pa-md">
      <q-spinner size="40px" color="primary" />
      <div class="q-mt-sm">Loading dashboard data...</div>
    </div>

    <!-- Account Summary -->
    <div class="row q-col-gutter-md q-mb-lg">
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">Total Balance</div>
            <div class="text-h4 text-primary">{{ formatCurrency(totalBalance) }}</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">Monthly Income</div>
            <div class="text-h4 text-positive">{{ formatCurrency(monthlyIncome) }}</div>
          </q-card-section>
        </q-card>
      </div>
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">Monthly Expenses</div>
            <div class="text-h4 text-negative">{{ formatCurrency(monthlyExpense) }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="row q-col-gutter-md">
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6 q-mb-md">Quick Actions</div>
            <div class="row q-col-gutter-sm">
              <div class="col-6 col-md-3">
                <q-btn 
                  color="primary" 
                  icon="add" 
                  label="Add Transaction" 
                  class="full-width"
                  @click="$router.push('/transactions/new')"
                />
              </div>
              <div class="col-6 col-md-3">
                <q-btn 
                  color="secondary" 
                  icon="account_balance" 
                  label="Accounts" 
                  class="full-width"
                  @click="$router.push('/accounts')"
                />
              </div>
              <div class="col-6 col-md-3">
                <q-btn 
                  color="info" 
                  icon="category" 
                  label="Categories" 
                  class="full-width"
                  @click="$router.push('/categories')"
                />
              </div>
              <div class="col-6 col-md-3">
                <q-btn 
                  color="warning" 
                  icon="bar_chart" 
                  label="Statistics" 
                  class="full-width"
                  @click="$router.push('/statistics')"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// Simple state for testing
const loading = ref(false);
const error = ref<string | null>(null);

// Mock data for testing
const totalBalance = ref(5000);
const monthlyIncome = ref(3000);
const monthlyExpense = ref(2000);

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};
</script>

<style scoped>
.dashboard-card {
  height: 100%;
}
</style>
