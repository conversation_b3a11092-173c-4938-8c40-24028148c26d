import { defineStore } from 'pinia'
import { ref } from 'vue'
import { api } from 'src/boot/axios'

export interface Category {
  id: number
  name: string
  type: 'income' | 'expense'
  icon: string
  color: string
  createdAt: string
  updatedAt: string
}

export const useCategoriesStore = defineStore('categories', () => {
  const categories = ref<Category[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 加载分类列表
  async function loadCategories() {
    loading.value = true
    error.value = null
    try {
      const response = await api.get<Category[]>('/categories')
      categories.value = response.data
    } catch (err) {
      console.error('Failed to load categories:', err)
      error.value = '加载分类失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 创建分类
  async function createCategory(data: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>) {
    loading.value = true
    error.value = null
    try {
      const response = await api.post<Category>('/categories', data)
      categories.value.push(response.data)
      return response.data
    } catch (err) {
      console.error('Failed to create category:', err)
      error.value = '创建分类失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新分类
  async function updateCategory(id: number, data: Partial<Category>) {
    loading.value = true
    error.value = null
    try {
      const response = await api.put<Category>(`/categories/${id}`, data)
      const index = categories.value.findIndex(c => c.id === id)
      if (index !== -1) {
        categories.value[index] = response.data
      }
      return response.data
    } catch (err) {
      console.error('Failed to update category:', err)
      error.value = '更新分类失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除分类
  async function deleteCategory(id: number) {
    loading.value = true
    error.value = null
    try {
      await api.delete(`/categories/${id}`)
      categories.value = categories.value.filter(c => c.id !== id)
    } catch (err) {
      console.error('Failed to delete category:', err)
      error.value = '删除分类失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    categories,
    loading,
    error,
    loadCategories,
    createCategory,
    updateCategory,
    deleteCategory
  }
}) 