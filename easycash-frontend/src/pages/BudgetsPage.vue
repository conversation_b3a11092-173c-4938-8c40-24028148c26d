<template>
  <q-page class="q-pa-md">
    <div class="row items-center q-mb-md">
      <div class="col">
        <div class="text-h5">Budgets</div>
        <div class="text-subtitle2 text-grey-7">Manage your spending limits</div>
      </div>
      <div class="col-auto">
        <q-btn 
          color="primary" 
          icon="add" 
          label="New Budget" 
          @click="openBudgetDialog()"
        />
      </div>
    </div>

    <!-- Budget Period Selector -->
    <q-card class="q-mb-md">
      <q-card-section class="q-pa-sm">
        <q-tabs
          v-model="activePeriod"
          dense
          class="text-grey"
          active-color="primary"
          indicator-color="primary"
          align="justify"
          narrow-indicator
          @update:model-value="fetchBudgets"
        >
          <q-tab name="monthly" label="Monthly" />
          <q-tab name="yearly" label="Yearly" />
          <q-tab name="custom" label="Custom Period" />
        </q-tabs>
      </q-card-section>
    </q-card>

    <!-- Budget Summary Card -->
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6">Budget Summary</div>
            <div class="text-subtitle2 text-grey-7">{{ getPeriodLabel() }}</div>
          </q-card-section>
          
          <q-separator />
          
          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col-6">
                <div class="text-subtitle2 text-grey-7">Total Budget</div>
                <div class="text-h5">{{ formatCurrency(summaryStats.totalBudget) }}</div>
              </div>
              <div class="col-6">
                <div class="text-subtitle2 text-grey-7">Spent So Far</div>
                <div class="text-h5" :class="getBudgetStatusColor(summaryStats.spentAmount, summaryStats.totalBudget, 'text')">
                  {{ formatCurrency(summaryStats.spentAmount) }}
                </div>
              </div>
            </div>
            
            <div class="q-mt-md">
              <div class="row items-center justify-between q-mb-xs">
                <div class="text-body2">Overall Progress</div>
                <div class="text-body2">
                  {{ Math.round((summaryStats.spentAmount / summaryStats.totalBudget) * 100) }}%
                </div>
              </div>
              <q-linear-progress
                :value="summaryStats.spentAmount / summaryStats.totalBudget"
                :color="getBudgetStatusColor(summaryStats.spentAmount, summaryStats.totalBudget)"
                size="15px"
                track-color="grey-3"
                rounded
              />
            </div>
            
            <div class="row q-col-gutter-md q-mt-md">
              <div class="col-6">
                <div class="text-subtitle2 text-grey-7">Remaining</div>
                <div class="text-h6 text-positive">
                  {{ formatCurrency(summaryStats.totalBudget - summaryStats.spentAmount) }}
                </div>
              </div>
              <div class="col-6">
                <div class="text-subtitle2 text-grey-7">Days Left</div>
                <div class="text-h6">{{ summaryStats.daysLeft }}</div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6">Budget Analysis</div>
            <div class="text-subtitle2 text-grey-7">Category Breakdown</div>
          </q-card-section>
          
          <q-separator />
          
          <q-card-section>
            <!-- Placeholder for a pie chart, in a real app we'd use a charting library -->
            <div class="budget-chart-placeholder">
              <div class="pie-chart"></div>
              <div class="legend">
                <div v-for="(status, index) in budgetStatus" :key="index" class="legend-item">
                  <div class="legend-color" :style="{ backgroundColor: status.color }"></div>
                  <div class="legend-label">{{ status.label }}</div>
                  <div class="legend-value">{{ status.count }} categories</div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Budget Categories List -->
    <q-card>
      <q-card-section v-if="budgets.length === 0" class="text-center q-py-lg">
        <q-icon name="savings" size="4rem" color="grey-5" />
        <div class="text-h6 q-mt-md">No Budgets Set</div>
        <div class="text-body2 text-grey-7 q-mb-md">Start managing your spending by creating a budget</div>
        <q-btn color="primary" label="Create Budget" icon="add" @click="openBudgetDialog()" />
      </q-card-section>
      
      <template v-else>
        <q-table
          :rows="budgets"
          :columns="columns"
          row-key="id"
          :loading="loading"
          :pagination="{rowsPerPage: 0}"
          hide-pagination
          flat
        >
          <!-- Custom header slots -->
          <template v-slot:header="props">
            <q-tr :props="props">
              <q-th v-for="col in props.cols" :key="col.name" :props="props">
                {{ col.label }}
              </q-th>
              <q-th auto-width>Actions</q-th>
            </q-tr>
          </template>
          
          <!-- Custom body slots -->
          <template v-slot:body="props">
            <q-tr :props="props">
              <q-td key="category" :props="props">
                <div class="row items-center">
                  <q-avatar :color="getCategoryColor(props.row.category)" size="28px" text-color="white">
                    <q-icon :name="getCategoryIcon(props.row.category)" size="18px" />
                  </q-avatar>
                  <div class="q-ml-sm">{{ props.row.category }}</div>
                </div>
              </q-td>
              <q-td key="amount" :props="props">
                {{ formatCurrency(props.row.amount) }}
              </q-td>
              <q-td key="spent" :props="props">
                {{ formatCurrency(props.row.spent) }}
              </q-td>
              <q-td key="remaining" :props="props">
                <div :class="props.row.remaining >= 0 ? 'text-positive' : 'text-negative'">
                  {{ formatCurrency(props.row.remaining) }}
                </div>
              </q-td>
              <q-td key="progress" :props="props">
                <div class="row items-center no-wrap">
                  <q-linear-progress
                    :value="props.row.spent / props.row.amount"
                    :color="getBudgetStatusColor(props.row.spent, props.row.amount)"
                    size="10px"
                    class="col"
                  />
                  <div class="q-ml-sm col-auto">
                    {{ Math.round((props.row.spent / props.row.amount) * 100) }}%
                  </div>
                </div>
              </q-td>
              <q-td auto-width>
                <q-btn flat round dense icon="edit" color="primary" @click="openBudgetDialog(props.row)" />
                <q-btn flat round dense icon="delete" color="negative" @click="confirmDelete(props.row)" />
              </q-td>
            </q-tr>
          </template>
          
          <!-- Loading state -->
          <template v-slot:loading>
            <q-inner-loading showing color="primary" />
          </template>
        </q-table>
      </template>
    </q-card>

    <!-- Budget Form Dialog -->
    <q-dialog v-model="budgetDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section class="row items-center">
          <div class="text-h6">{{ editMode ? 'Edit Budget' : 'New Budget' }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section>
          <q-form @submit="saveBudget" class="q-gutter-md">
            <q-select
              v-model="currentBudget.category"
              :options="categoryOptions"
              label="Category"
              outlined
              emit-value
              map-options
              :rules="[val => !!val || 'Category is required']"
            >
              <template v-slot:option="scope">
                <q-item v-bind="scope.itemProps">
                  <q-item-section avatar>
                    <q-icon :name="getCategoryIcon(scope.opt.value)" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ scope.opt.label }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </q-select>
            
            <q-input
              v-model.number="currentBudget.amount"
              label="Budget Amount"
              outlined
              type="number"
              :rules="[
                val => val !== null && val !== undefined || 'Amount is required',
                val => val > 0 || 'Amount must be greater than zero'
              ]"
            >
              <template v-slot:prepend>
                <q-icon name="attach_money" />
              </template>
            </q-input>
            
            <q-select
              v-model="currentBudget.period"
              :options="periodOptions"
              label="Budget Period"
              outlined
              emit-value
              map-options
              :rules="[val => !!val || 'Period is required']"
              @update:model-value="updateDateFields"
            />
            
            <div class="row q-col-gutter-md">
              <div class="col-6">
                <q-input
                  v-model="currentBudget.startDate"
                  outlined
                  label="Start Date"
                  :disable="currentBudget.period !== 'custom'"
                  :rules="[val => !!val || 'Start date is required']"
                >
                  <template v-slot:append>
                    <q-icon name="event" class="cursor-pointer">
                      <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                        <q-date v-model="currentBudget.startDate" mask="YYYY-MM-DD" />
                      </q-popup-proxy>
                    </q-icon>
                  </template>
                </q-input>
              </div>
              
              <div class="col-6">
                <q-input
                  v-model="currentBudget.endDate"
                  outlined
                  label="End Date"
                  :disable="currentBudget.period !== 'custom'"
                  :rules="[
                    val => !!val || 'End date is required',
                    val => new Date(val) >= new Date(currentBudget.startDate) || 'End date must be after start date'
                  ]"
                >
                  <template v-slot:append>
                    <q-icon name="event" class="cursor-pointer">
                      <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                        <q-date v-model="currentBudget.endDate" mask="YYYY-MM-DD" />
                      </q-popup-proxy>
                    </q-icon>
                  </template>
                </q-input>
              </div>
            </div>
            
            <q-input
              v-model="currentBudget.notes"
              label="Notes"
              outlined
              type="textarea"
            />
            
            <div class="row q-col-gutter-sm q-mt-md">
              <div class="col">
                <q-btn
                  color="primary"
                  label="Save"
                  type="submit"
                  class="full-width"
                />
              </div>
              <div class="col">
                <q-btn
                  color="grey-7"
                  label="Cancel"
                  v-close-popup
                  class="full-width"
                />
              </div>
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="negative" text-color="white" />
          <span class="q-ml-sm">Delete Budget</span>
        </q-card-section>

        <q-card-section>
          Are you sure you want to delete this budget? This will not affect any existing transactions.
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Delete" color="negative" @click="deleteBudget" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';

// 定义预算项接口
interface Budget {
  id: number | null;
  category: string;
  amount: number;
  spent: number;
  period: string;
  startDate: string;
  endDate: string;
  notes: string;
  remaining?: number;
}

// 定义状态计数接口
interface StatusCounts {
  onTrack: number;
  warning: number;
  exceeded: number;
}

const $q = useQuasar();
const activePeriod = ref('monthly');
const loading = ref(false);

// Table configuration
const columns = [
  { name: 'category', label: 'Category', field: 'category', sortable: true },
  { name: 'amount', label: 'Budget', field: 'amount', sortable: true },
  { name: 'spent', label: 'Spent', field: 'spent', sortable: true },
  { name: 'remaining', label: 'Remaining', field: 'remaining', sortable: true },
  { name: 'progress', label: 'Progress', field: 'progress' }
];

// State variables
const budgets = ref<Budget[]>([]);
const budgetDialog = ref(false);
const deleteDialog = ref(false);
const editMode = ref(false);
const budgetToDelete = ref<Budget | null>(null);
const currentBudget = ref<Budget>({
  id: null,
  category: '',
  amount: 0,
  spent: 0,
  period: 'monthly',
  startDate: '',
  endDate: '',
  notes: ''
});

// Summary statistics
const summaryStats = ref({
  totalBudget: 0,
  spentAmount: 0,
  daysLeft: 0
});

// Budget status counts
const budgetStatus = ref([
  { label: 'On Track', count: 0, color: '#4CAF50' },
  { label: 'Warning', count: 0, color: '#FF9800' },
  { label: 'Exceeded', count: 0, color: '#F44336' }
]);

// Options for dropdowns
const categoryOptions = [
  { label: 'Food', value: 'Food' },
  { label: 'Transportation', value: 'Transportation' },
  { label: 'Housing', value: 'Housing' },
  { label: 'Entertainment', value: 'Entertainment' },
  { label: 'Shopping', value: 'Shopping' },
  { label: 'Health', value: 'Health' },
  { label: 'Education', value: 'Education' }
];

const periodOptions = [
  { label: 'Monthly', value: 'monthly' },
  { label: 'Yearly', value: 'yearly' },
  { label: 'Custom Period', value: 'custom' }
];

// Helper functions
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(value);
};

const getCategoryIcon = (category: string) => {
  const icons: Record<string, string> = {
    'Food': 'restaurant',
    'Transportation': 'directions_car',
    'Housing': 'home',
    'Entertainment': 'movie',
    'Shopping': 'shopping_bag',
    'Health': 'health_and_safety',
    'Education': 'school',
    'Salary': 'payments'
  };
  
  return icons[category] || 'category';
};

const getCategoryColor = (category: string) => {
  const colors: Record<string, string> = {
    'Food': 'red',
    'Transportation': 'blue',
    'Housing': 'teal',
    'Entertainment': 'purple',
    'Shopping': 'pink',
    'Health': 'green',
    'Education': 'amber',
    'Salary': 'positive'
  };
  
  return colors[category] || 'grey';
};

const getBudgetStatusColor = (spent: number, budget: number, type = 'bg') => {
  if (!spent || !budget) return type === 'text' ? 'text-grey' : 'grey';
  
  const ratio = spent / budget;
  
  if (ratio >= 1) {
    return type === 'text' ? 'text-negative' : 'negative';
  } else if (ratio >= 0.8) {
    return type === 'text' ? 'text-warning' : 'warning';
  } else {
    return type === 'text' ? 'text-positive' : 'positive';
  }
};

const getPeriodLabel = () => {
  const date = new Date();
  
  if (activePeriod.value === 'monthly') {
    return `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;
  } else if (activePeriod.value === 'yearly') {
    return `${date.getFullYear()}`;
  } else {
    return 'Custom Period';
  }
};

// Date handling
const updateDateFields = () => {
  const today = new Date();
  
  if (currentBudget.value.period === 'monthly') {
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    currentBudget.value.startDate = startOfMonth.toISOString().split('T')[0];
    currentBudget.value.endDate = endOfMonth.toISOString().split('T')[0];
  } else if (currentBudget.value.period === 'yearly') {
    const startOfYear = new Date(today.getFullYear(), 0, 1);
    const endOfYear = new Date(today.getFullYear(), 11, 31);
    
    currentBudget.value.startDate = startOfYear.toISOString().split('T')[0];
    currentBudget.value.endDate = endOfYear.toISOString().split('T')[0];
  }
};

// CRUD operations
const fetchBudgets = async () => {
  loading.value = true;
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock data
    const mockBudgets = [
      { id: 1, category: 'Food', amount: 500, spent: 350.25, period: 'monthly', startDate: '2023-09-01', endDate: '2023-09-30', notes: '' },
      { id: 2, category: 'Transportation', amount: 300, spent: 250.75, period: 'monthly', startDate: '2023-09-01', endDate: '2023-09-30', notes: '' },
      { id: 3, category: 'Housing', amount: 1500, spent: 1500, period: 'monthly', startDate: '2023-09-01', endDate: '2023-09-30', notes: 'Rent payment' },
      { id: 4, category: 'Entertainment', amount: 200, spent: 75.50, period: 'monthly', startDate: '2023-09-01', endDate: '2023-09-30', notes: '' },
      { id: 5, category: 'Shopping', amount: 250, spent: 315.95, period: 'monthly', startDate: '2023-09-01', endDate: '2023-09-30', notes: 'Exceeded due to birthday gift purchase' }
    ];
    
    // Filter based on active period
    budgets.value = mockBudgets.filter(budget => budget.period === activePeriod.value);
    
    // Calculate remaining amounts
    budgets.value = budgets.value.map(budget => ({
      ...budget,
      remaining: budget.amount - budget.spent
    }));
    
    // Update summary statistics
    summaryStats.value.totalBudget = budgets.value.reduce((sum, budget) => sum + budget.amount, 0);
    summaryStats.value.spentAmount = budgets.value.reduce((sum, budget) => sum + budget.spent, 0);
    
    // Calculate days left in current period
    const today = new Date();
    const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    summaryStats.value.daysLeft = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    // Update budget status counts
    const statuses = budgets.value.reduce((counts: StatusCounts, budget) => {
      const ratio = budget.spent / budget.amount;
      
      if (ratio >= 1) {
        counts.exceeded++;
      } else if (ratio >= 0.8) {
        counts.warning++;
      } else {
        counts.onTrack++;
      }
      
      return counts;
    }, { onTrack: 0, warning: 0, exceeded: 0 });
    
    budgetStatus.value[0].count = statuses.onTrack;
    budgetStatus.value[1].count = statuses.warning;
    budgetStatus.value[2].count = statuses.exceeded;
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Failed to load budgets:', err);
    $q.notify({
      color: 'negative',
      message: 'Failed to load budgets',
      icon: 'error'
    });
  } finally {
    loading.value = false;
  }
};

const openBudgetDialog = (budget: Budget | null = null) => {
  if (budget) {
    // Edit mode
    currentBudget.value = { ...budget };
    editMode.value = true;
  } else {
    // Create mode
    currentBudget.value = {
      id: null,
      category: '',
      amount: 0,
      spent: 0,
      period: activePeriod.value,
      startDate: '',
      endDate: '',
      notes: ''
    };
    
    updateDateFields();
    editMode.value = false;
  }
  
  budgetDialog.value = true;
};

const saveBudget = async () => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (editMode.value) {
      // Update existing budget
      const index = budgets.value.findIndex((b) => b.id === currentBudget.value.id);
      if (index !== -1) {
        const updatedBudget = {
          ...currentBudget.value,
          remaining: currentBudget.value.amount - currentBudget.value.spent
        };
        budgets.value[index] = updatedBudget;
      }
      
      $q.notify({
        color: 'positive',
        message: 'Budget updated successfully',
        icon: 'check_circle'
      });
    } else {
      // Create new budget
      const newId = Math.max(...budgets.value.map((b) => b.id || 0), 0) + 1;
      const newBudget = {
        ...currentBudget.value,
        id: newId,
        remaining: currentBudget.value.amount - currentBudget.value.spent
      };
      
      budgets.value.push(newBudget);
      
      $q.notify({
        color: 'positive',
        message: 'Budget created successfully',
        icon: 'check_circle'
      });
    }
    
    // Update summary statistics
    summaryStats.value.totalBudget = budgets.value.reduce((sum, budget) => sum + budget.amount, 0);
    summaryStats.value.spentAmount = budgets.value.reduce((sum, budget) => sum + budget.spent, 0);
    
    // Update budget status counts
    const statuses = budgets.value.reduce((counts: StatusCounts, budget) => {
      const ratio = budget.spent / budget.amount;
      
      if (ratio >= 1) {
        counts.exceeded++;
      } else if (ratio >= 0.8) {
        counts.warning++;
      } else {
        counts.onTrack++;
      }
      
      return counts;
    }, { onTrack: 0, warning: 0, exceeded: 0 });
    
    budgetStatus.value[0].count = statuses.onTrack;
    budgetStatus.value[1].count = statuses.warning;
    budgetStatus.value[2].count = statuses.exceeded;
    
    budgetDialog.value = false;
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Failed to save budget:', err);
    $q.notify({
      color: 'negative',
      message: editMode.value ? 'Failed to update budget' : 'Failed to create budget',
      icon: 'error'
    });
  }
};

const confirmDelete = (budget: Budget) => {
  budgetToDelete.value = budget;
  deleteDialog.value = true;
};

const deleteBudget = async () => {
  if (!budgetToDelete.value) return;
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Remove from local array
    const index = budgets.value.findIndex((b) => b.id === budgetToDelete.value.id);
    if (index !== -1) {
      budgets.value.splice(index, 1);
    }
    
    $q.notify({
      color: 'positive',
      message: 'Budget deleted successfully',
      icon: 'check_circle'
    });
    
    // Update summary statistics
    summaryStats.value.totalBudget = budgets.value.reduce((sum, budget) => sum + budget.amount, 0);
    summaryStats.value.spentAmount = budgets.value.reduce((sum, budget) => sum + budget.spent, 0);
    
    // Update budget status counts
    const statuses = budgets.value.reduce((counts: StatusCounts, budget) => {
      const ratio = budget.spent / budget.amount;
      
      if (ratio >= 1) {
        counts.exceeded++;
      } else if (ratio >= 0.8) {
        counts.warning++;
      } else {
        counts.onTrack++;
      }
      
      return counts;
    }, { onTrack: 0, warning: 0, exceeded: 0 });
    
    budgetStatus.value[0].count = statuses.onTrack;
    budgetStatus.value[1].count = statuses.warning;
    budgetStatus.value[2].count = statuses.exceeded;
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Failed to delete budget:', err);
    $q.notify({
      color: 'negative',
      message: 'Failed to delete budget',
      icon: 'error'
    });
  } finally {
    budgetToDelete.value = null;
  }
};

// Initialize
onMounted(() => {
  void fetchBudgets();
});
</script>

<style lang="scss" scoped>
// Budget chart placeholder styling
.budget-chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .pie-chart {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: conic-gradient(
      #4CAF50 0% 50%,
      #FF9800 50% 70%,
      #F44336 70% 100%
    );
    margin-bottom: 20px;
  }
  
  .legend {
    display: flex;
    flex-direction: column;
    width: 100%;
    
    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .legend-color {
        width: 15px;
        height: 15px;
        margin-right: 8px;
      }
      
      .legend-label {
        flex: 1;
      }
    }
  }
}
</style> 