<template>
  <q-page padding>
    <page-header title="Dashboard" />
    
    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading" message="Loading dashboard data..." />
    
    <!-- Account Summary -->
    <div class="row q-col-gutter-md q-mb-lg">
      <div class="col-12 col-md-4">
        <q-card class="dashboard-card">
          <q-card-section>
            <div class="text-subtitle1 text-grey">Total Balance</div>
            <div class="text-h4 text-primary">{{ formatCurrency(totalBalance) }}</div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-12 col-md-4">
        <q-card class="dashboard-card">
          <q-card-section>
            <div class="text-subtitle1 text-grey">Income (This Month)</div>
            <div class="text-h4 text-positive">{{ formatCurrency(monthlyIncome) }}</div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-12 col-md-4">
        <q-card class="dashboard-card">
          <q-card-section>
            <div class="text-subtitle1 text-grey">Expenses (This Month)</div>
            <div class="text-h4 text-negative">{{ formatCurrency(monthlyExpense) }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    
    <!-- Recent Transactions -->
    <div class="row q-mb-lg">
      <div class="col-12">
        <q-card>
          <q-card-section class="q-pb-none">
            <div class="text-h6">Recent Transactions</div>
          </q-card-section>
          
          <q-card-section>
            <q-list v-if="recentTransactions.length > 0" separator>
              <q-item v-for="transaction in recentTransactions" :key="transaction.id" clickable @click="viewTransaction(transaction.id)">
                <q-item-section avatar>
                  <q-avatar :color="getTransactionColor(transaction.type)" text-color="white">
                    <q-icon :name="getTransactionIcon(transaction.type)" />
                  </q-avatar>
                </q-item-section>
                
                <q-item-section>
                  <q-item-label>{{ transaction.description || 'No description' }}</q-item-label>
                  <q-item-label caption>
                    {{ transaction.category?.name || 'Uncategorized' }} • {{ formatDate(transaction.date) }}
                  </q-item-label>
                </q-item-section>
                
                <q-item-section side>
                  <q-item-label :class="getAmountClass(transaction.type)">
                    {{ getAmountPrefix(transaction.type) }}{{ formatCurrency(transaction.amount) }}
                  </q-item-label>
                  <q-item-label caption>{{ transaction.account?.name || 'Unknown account' }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
            
            <div v-else class="text-center q-pa-md">
              <q-icon name="receipt_long" size="3rem" color="grey-5" />
              <div class="text-subtitle1 q-mt-sm">No recent transactions</div>
              <q-btn color="primary" label="Add Transaction" class="q-mt-sm" to="/transactions/new" />
            </div>
          </q-card-section>
          
          <q-card-actions align="right">
            <q-btn flat color="primary" label="View All" to="/transactions" />
          </q-card-actions>
        </q-card>
      </div>
    </div>
    
    <!-- Budget Progress -->
    <div class="row q-mb-lg">
      <div class="col-12">
        <q-card>
          <q-card-section class="q-pb-none">
            <div class="text-h6">Budget Progress</div>
          </q-card-section>
          
          <q-card-section>
            <div v-if="activeBudgets.length > 0">
              <div v-for="budget in activeBudgets" :key="budget.id" class="q-mb-md">
                <div class="row items-center justify-between q-mb-xs">
                  <div class="col">{{ budget.category?.name || 'Uncategorized' }}</div>
                  <div class="col-auto">
                    {{ formatCurrency(budget.spent_amount || 0) }} / {{ formatCurrency(budget.amount) }}
                  </div>
                </div>
                <q-linear-progress
                  :value="getBudgetProgress(budget)"
                  :color="getBudgetColor(budget)"
                  size="md"
                />
              </div>
            </div>
            
            <div v-else class="text-center q-pa-md">
              <q-icon name="account_balance_wallet" size="3rem" color="grey-5" />
              <div class="text-subtitle1 q-mt-sm">No active budgets</div>
              <q-btn color="primary" label="Create Budget" class="q-mt-sm" to="/budgets/new" />
            </div>
          </q-card-section>
          
          <q-card-actions align="right">
            <q-btn flat color="primary" label="View All" to="/budgets" />
          </q-card-actions>
        </q-card>
      </div>
    </div>
    
    <!-- Accounts Overview -->
    <div class="row">
      <div class="col-12">
        <q-card>
          <q-card-section class="q-pb-none">
            <div class="text-h6">Accounts Overview</div>
          </q-card-section>
          
          <q-card-section>
            <q-list v-if="accounts.length > 0" separator>
              <q-item v-for="account in accounts" :key="account.id" clickable @click="viewAccount(account.id)">
                <q-item-section avatar>
                  <q-avatar :color="account.color || 'primary'" text-color="white">
                    <q-icon :name="account.icon || 'account_balance'" />
                  </q-avatar>
                </q-item-section>
                
                <q-item-section>
                  <q-item-label>{{ account.name }}</q-item-label>
                  <q-item-label caption>{{ account.type }}</q-item-label>
                </q-item-section>
                
                <q-item-section side>
                  <q-item-label>{{ formatCurrency(account.balance) }}</q-item-label>
                  <q-item-label caption>{{ account.currency }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
            
            <div v-else class="text-center q-pa-md">
              <q-icon name="account_balance" size="3rem" color="grey-5" />
              <div class="text-subtitle1 q-mt-sm">No accounts</div>
              <q-btn color="primary" label="Add Account" class="q-mt-sm" to="/accounts/new" />
            </div>
          </q-card-section>
          
          <q-card-actions align="right">
            <q-btn flat color="primary" label="View All" to="/accounts" />
          </q-card-actions>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { date } from 'quasar';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import { useAccountStore } from 'src/stores/account.store';
import { useTransactionStore } from 'src/stores/transaction.store';
import { useStatisticsStore } from 'src/stores/statistics.store';
import { useBudgetStore } from 'src/stores/budget.store';
import { Transaction } from 'src/services/transaction.service';
import { Budget } from 'src/services/budget.service';
import NotificationService from 'src/services/notification.service';

const router = useRouter();
const accountStore = useAccountStore();
const transactionStore = useTransactionStore();
const statisticsStore = useStatisticsStore();
const budgetStore = useBudgetStore();

const loading = ref(false);
const error = ref<string | null>(null);

// Computed properties
const accounts = computed(() => accountStore.accounts);
const totalBalance = computed(() => accountStore.totalBalance);
const recentTransactions = computed(() => transactionStore.transactions.slice(0, 5));
const activeBudgets = computed(() => budgetStore.activeBudgets);
const monthlyIncome = computed(() => statisticsStore.monthlyIncome);
const monthlyExpense = computed(() => statisticsStore.monthlyExpense);

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Format date
const formatDate = (dateString: string) => {
  return date.formatDate(dateString, 'MMM D, YYYY');
};

// Get transaction icon
const getTransactionIcon = (type: string) => {
  switch (type) {
    case 'income':
      return 'arrow_upward';
    case 'expense':
      return 'arrow_downward';
    case 'transfer':
      return 'swap_horiz';
    default:
      return 'receipt_long';
  }
};

// Get transaction color
const getTransactionColor = (type: string) => {
  switch (type) {
    case 'income':
      return 'positive';
    case 'expense':
      return 'negative';
    case 'transfer':
      return 'info';
    default:
      return 'grey';
  }
};

// Get amount class
const getAmountClass = (type: string) => {
  switch (type) {
    case 'income':
      return 'text-positive';
    case 'expense':
      return 'text-negative';
    default:
      return '';
  }
};

// Get amount prefix
const getAmountPrefix = (type: string) => {
  switch (type) {
    case 'income':
      return '+';
    case 'expense':
      return '-';
    default:
      return '';
  }
};

// Get budget progress
const getBudgetProgress = (budget: Budget) => {
  if (budget.amount <= 0) return 0;
  const progress = (budget.spent_amount || 0) / budget.amount;
  return Math.min(progress, 1); // Cap at 100%
};

// Get budget color
const getBudgetColor = (budget: Budget) => {
  const progress = getBudgetProgress(budget);
  if (progress >= 0.9) return 'negative';
  if (progress >= 0.7) return 'warning';
  return 'positive';
};

// View transaction
const viewTransaction = (id: number) => {
  router.push(`/transactions/${id}`);
};

// View account
const viewAccount = (id: number) => {
  router.push(`/accounts/${id}`);
};

// Load dashboard data
const loadDashboardData = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    // Get current date
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    // Format dates for API
    const startDate = date.formatDate(startOfMonth, 'YYYY-MM-DD');
    const endDate = date.formatDate(endOfMonth, 'YYYY-MM-DD');
    
    // Load data in parallel
    await Promise.all([
      accountStore.fetchAccounts(),
      transactionStore.list({ 
        limit: 5, 
        sort_by: 'date', 
        sort_order: 'desc' 
      }),
      budgetStore.fetchActiveBudgets(),
      statisticsStore.fetchMonthlyStatistics(startDate, endDate)
    ]);
  } catch (err: any) {
    error.value = 'Failed to load dashboard data. Please try again.';
    NotificationService.error('Failed to load dashboard data');
    console.error('Dashboard error:', err);
  } finally {
    loading.value = false;
  }
};

// Load data on component mount
onMounted(() => {
  loadDashboardData();
});
</script>

<style scoped>
.dashboard-card {
  height: 100%;
}
</style>
