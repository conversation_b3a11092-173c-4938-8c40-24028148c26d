import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import TransactionService, {
  Transaction as ServiceTransaction,
  TransactionRequest,
  TransactionListParams
} from 'src/services/transaction.service'
import type { Transaction } from 'src/types/models/transaction'
import { adaptServiceTransactionsToModel, adaptServiceTransactionToModel } from 'src/utils/adapters'

export const useTransactionStore = defineStore('transaction', () => {
  // State
  const transactions = ref<Transaction[]>([])
  const currentTransaction = ref<Transaction | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const pagination = ref({
    total: 0,
    page: 1,
    pageSize: 20
  })

  // Getters
  const expenseTransactions = computed(() => {
    return transactions.value.filter(transaction => transaction.type === 'expense')
  })

  const incomeTransactions = computed(() => {
    return transactions.value.filter(transaction => transaction.type === 'income')
  })

  const transferTransactions = computed(() => {
    return transactions.value.filter(transaction => transaction.type === 'transfer')
  })

  const totalExpense = computed(() => {
    return expenseTransactions.value.reduce((sum, transaction) => sum + transaction.amount, 0)
  })

  const totalIncome = computed(() => {
    return incomeTransactions.value.reduce((sum, transaction) => sum + transaction.amount, 0)
  })

  const getById = computed(() => (id: number) =>
    transactions.value.find(t => t.id === id)
  )

  const getTransactionsByDate = computed(() => {
    return (date: string) => {
      return transactions.value.filter(transaction => {
        return transaction.date.substring(0, 10) === date
      })
    }
  })

  const getTransactionsByCategory = computed(() => {
    return (categoryId: number) => {
      return transactions.value.filter(transaction => transaction.categoryId === categoryId)
    }
  })

  const getTransactionsByAccount = computed(() => {
    return (accountId: number) => {
      return transactions.value.filter(transaction => transaction.accountId === accountId)
    }
  })

  // Actions
  async function list(params?: TransactionListParams) {
    loading.value = true
    error.value = null

    try {
      const response = await TransactionService.getTransactions(params)
      const adaptedTransactions = adaptServiceTransactionsToModel(response.items)
      transactions.value = adaptedTransactions
      pagination.value = {
        total: response.total,
        page: response.page,
        pageSize: response.page_size
      }
      return {
        items: adaptedTransactions,
        total: response.total,
        page: response.page,
        pageSize: response.page_size
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch transactions'
      throw error.value
    } finally {
      loading.value = false
    }
  }

  async function get(id: number) {
    loading.value = true
    error.value = null

    try {
      const serviceTransaction = await TransactionService.getTransaction(id)
      const adaptedTransaction = adaptServiceTransactionToModel(serviceTransaction)
      currentTransaction.value = adaptedTransaction
      return adaptedTransaction
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch transaction'
      throw error.value
    } finally {
      loading.value = false
    }
  }

  async function create(transactionData: TransactionRequest) {
    loading.value = true
    error.value = null

    try {
      const serviceTransaction = await TransactionService.createTransaction(transactionData)
      const adaptedTransaction = adaptServiceTransactionToModel(serviceTransaction)
      transactions.value.unshift(adaptedTransaction) // Add to beginning of array
      return adaptedTransaction
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to create transaction'
      throw error.value
    } finally {
      loading.value = false
    }
  }

  async function update(id: number, transactionData: TransactionRequest) {
    loading.value = true
    error.value = null

    try {
      const serviceTransaction = await TransactionService.updateTransaction(id, transactionData)
      const adaptedTransaction = adaptServiceTransactionToModel(serviceTransaction)

      // Update the transaction in the transactions array
      const index = transactions.value.findIndex(t => t.id === id)
      if (index !== -1) {
        transactions.value[index] = adaptedTransaction
      }

      // Update currentTransaction if it's the same transaction
      if (currentTransaction.value?.id === id) {
        currentTransaction.value = adaptedTransaction
      }

      return adaptedTransaction
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to update transaction'
      throw error.value
    } finally {
      loading.value = false
    }
  }

  async function delete_(id: number) {
    loading.value = true
    error.value = null

    try {
      await TransactionService.deleteTransaction(id)

      // Remove the transaction from the transactions array
      transactions.value = transactions.value.filter(t => t.id !== id)

      // Clear currentTransaction if it's the same transaction
      if (currentTransaction.value?.id === id) {
        currentTransaction.value = null
      }
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to delete transaction'
      throw error.value
    } finally {
      loading.value = false
    }
  }

  return {
    // State
    transactions,
    currentTransaction,
    loading,
    error,
    pagination,

    // Getters
    expenseTransactions,
    incomeTransactions,
    transferTransactions,
    totalExpense,
    totalIncome,
    getById,
    getTransactionsByDate,
    getTransactionsByCategory,
    getTransactionsByAccount,

    // Actions
    list,
    get,
    create,
    update,
    delete: delete_
  }
})