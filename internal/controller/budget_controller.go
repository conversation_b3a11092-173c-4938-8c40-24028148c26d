package controller

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/imaojia/easycash/internal/middleware"
	"github.com/imaojia/easycash/internal/model"
	"github.com/imaojia/easycash/internal/pkg/response"
	"github.com/imaojia/easycash/internal/service"
)

// BudgetController handles budget related requests
type BudgetController struct {
	budgetService *service.BudgetService
}

// NewBudgetController creates a new budget controller
func NewBudgetController(budgetService *service.BudgetService) *BudgetController {
	return &BudgetController{budgetService: budgetService}
}

// CreateBudget handles budget creation
// @Summary Create a new budget
// @Description Create a new budget for the current user
// @Tags budgets
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.BudgetRequest true "Budget details"
// @Success 200 {object} response.Response{data=model.BudgetResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/budgets [post]
func (c *BudgetController) CreateBudget(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	var req model.BudgetRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	budget, err := c.budgetService.CreateBudget(userID, req)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, budget)
}

// GetBudget retrieves a budget by ID
// @Summary Get budget by ID
// @Description Get a budget by its ID
// @Tags budgets
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Budget ID"
// @Success 200 {object} response.Response{data=model.BudgetResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/budgets/{id} [get]
func (c *BudgetController) GetBudget(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	budgetID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid budget ID")
		return
	}

	budget, err := c.budgetService.GetBudgetWithStats(userID, uint(budgetID))
	if err != nil {
		response.NotFound(ctx, "budget not found")
		return
	}

	response.Success(ctx, budget)
}

// UpdateBudget updates a budget
// @Summary Update a budget
// @Description Update a budget by its ID
// @Tags budgets
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Budget ID"
// @Param request body model.BudgetRequest true "Budget details"
// @Success 200 {object} response.Response{data=model.BudgetResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/budgets/{id} [put]
func (c *BudgetController) UpdateBudget(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	budgetID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid budget ID")
		return
	}

	var req model.BudgetRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	budget, err := c.budgetService.UpdateBudget(userID, uint(budgetID), req)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, budget)
}

// DeleteBudget deletes a budget
// @Summary Delete a budget
// @Description Delete a budget by its ID
// @Tags budgets
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Budget ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/budgets/{id} [delete]
func (c *BudgetController) DeleteBudget(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	budgetID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid budget ID")
		return
	}

	if err := c.budgetService.DeleteBudget(userID, uint(budgetID)); err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, gin.H{"message": "budget deleted successfully"})
}

// ListBudgets retrieves budgets for the current user
// @Summary List budgets
// @Description List budgets for the current user
// @Tags budgets
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param category_id query int false "Category ID"
// @Param period query string false "Budget period"
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Page size" default(10)
// @Success 200 {object} response.Response{data=[]model.BudgetResponse}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/budgets [get]
func (c *BudgetController) ListBudgets(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	var params model.BudgetListParams
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}
	params.UserID = userID

	// Parse date parameters if provided
	if startDateStr := ctx.Query("start_date"); startDateStr != "" {
		startDate, err := time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.BadRequest(ctx, "invalid start date format, use YYYY-MM-DD")
			return
		}
		params.StartDate = &startDate
	}

	if endDateStr := ctx.Query("end_date"); endDateStr != "" {
		endDate, err := time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.BadRequest(ctx, "invalid end date format, use YYYY-MM-DD")
			return
		}
		params.EndDate = &endDate
	}

	// Set default values if not provided
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.Limit <= 0 {
		params.Limit = 10
	}

	budgets, total, err := c.budgetService.ListBudgets(params)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Convert to response objects with stats
	budgetResponses := make([]model.BudgetResponse, len(budgets))
	for i, budget := range budgets {
		// Calculate spent amount for each budget
		spentAmount, err := c.budgetService.(*service.BudgetService).calculateSpentAmount(
			userID, budget.CategoryID, budget.StartDate, budget.EndDate)
		if err != nil {
			response.InternalError(ctx, err.Error())
			return
		}

		budgetResponse := budget.ToResponseWithRelations()
		budgetResponse.SpentAmount = spentAmount
		if budget.Amount > 0 {
			budgetResponse.Percentage = (spentAmount / budget.Amount) * 100
		}

		budgetResponses[i] = budgetResponse
	}

	response.SuccessWithPagination(ctx, budgetResponses, total, params.Page, params.Limit)
}

// GetActiveBudgets retrieves active budgets for the current user
// @Summary Get active budgets
// @Description Get active budgets for the current user
// @Tags budgets
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=[]model.BudgetResponse}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/budgets/active [get]
func (c *BudgetController) GetActiveBudgets(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	budgets, err := c.budgetService.GetActiveBudgets(userID)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Convert to response objects with stats
	budgetResponses := make([]model.BudgetResponse, len(budgets))
	for i, budget := range budgets {
		// Calculate spent amount for each budget
		spentAmount, err := c.budgetService.(*service.BudgetService).calculateSpentAmount(
			userID, budget.CategoryID, budget.StartDate, budget.EndDate)
		if err != nil {
			response.InternalError(ctx, err.Error())
			return
		}

		budgetResponse := budget.ToResponseWithRelations()
		budgetResponse.SpentAmount = spentAmount
		if budget.Amount > 0 {
			budgetResponse.Percentage = (spentAmount / budget.Amount) * 100
		}

		budgetResponses[i] = budgetResponse
	}

	response.Success(ctx, budgetResponses)
}
