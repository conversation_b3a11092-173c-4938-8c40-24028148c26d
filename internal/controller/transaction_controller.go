package controller

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/imaojia/easycash/internal/middleware"
	"github.com/imaojia/easycash/internal/model"
	"github.com/imaojia/easycash/internal/pkg/response"
	"github.com/imaojia/easycash/internal/service"
)

// TransactionController handles transaction related requests
type TransactionController struct {
	transactionService *service.TransactionService
}

// NewTransactionController creates a new transaction controller
func NewTransactionController(transactionService *service.TransactionService) *TransactionController {
	return &TransactionController{transactionService: transactionService}
}

// CreateTransaction handles transaction creation
// @Summary Create a new transaction
// @Description Create a new transaction for the current user
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.TransactionRequest true "Transaction details"
// @Success 200 {object} response.Response{data=model.TransactionResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/transactions [post]
func (c *TransactionController) CreateTransaction(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	var req model.TransactionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	transaction, err := c.transactionService.CreateTransaction(userID, req)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, transaction)
}

// GetTransaction retrieves a transaction by ID
// @Summary Get transaction by ID
// @Description Get a transaction by its ID
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Transaction ID"
// @Success 200 {object} response.Response{data=model.TransactionResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/transactions/{id} [get]
func (c *TransactionController) GetTransaction(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	transactionID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid transaction ID")
		return
	}

	transaction, err := c.transactionService.GetTransactionByID(userID, uint(transactionID))
	if err != nil {
		response.NotFound(ctx, "transaction not found")
		return
	}

	response.Success(ctx, transaction.ToResponseWithRelations())
}

// UpdateTransaction updates a transaction
// @Summary Update a transaction
// @Description Update a transaction by its ID
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Transaction ID"
// @Param request body model.TransactionRequest true "Transaction details"
// @Success 200 {object} response.Response{data=model.TransactionResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/transactions/{id} [put]
func (c *TransactionController) UpdateTransaction(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	transactionID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid transaction ID")
		return
	}

	var req model.TransactionRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	transaction, err := c.transactionService.UpdateTransaction(userID, uint(transactionID), req)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, transaction)
}

// DeleteTransaction deletes a transaction
// @Summary Delete a transaction
// @Description Delete a transaction by its ID
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Transaction ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/transactions/{id} [delete]
func (c *TransactionController) DeleteTransaction(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	transactionID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid transaction ID")
		return
	}

	if err := c.transactionService.DeleteTransaction(userID, uint(transactionID)); err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, gin.H{"message": "transaction deleted successfully"})
}

// ListTransactions retrieves transactions for the current user
// @Summary List transactions
// @Description List transactions for the current user
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param account_id query int false "Account ID"
// @Param category_id query int false "Category ID"
// @Param type query string false "Transaction type"
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Param min_amount query number false "Minimum amount"
// @Param max_amount query number false "Maximum amount"
// @Param search query string false "Search term"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Page size" default(20)
// @Param sort_by query string false "Sort field" default(date)
// @Param sort_order query string false "Sort order" Enums(asc, desc) default(desc)
// @Success 200 {object} response.Response{data=[]model.TransactionResponse}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/transactions [get]
func (c *TransactionController) ListTransactions(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	var params model.TransactionListParams
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}
	params.UserID = userID

	// Parse date parameters if provided
	if startDateStr := ctx.Query("start_date"); startDateStr != "" {
		startDate, err := time.Parse("2006-01-02", startDateStr)
		if err != nil {
			response.BadRequest(ctx, "invalid start date format, use YYYY-MM-DD")
			return
		}
		params.StartDate = &startDate
	}

	if endDateStr := ctx.Query("end_date"); endDateStr != "" {
		endDate, err := time.Parse("2006-01-02", endDateStr)
		if err != nil {
			response.BadRequest(ctx, "invalid end date format, use YYYY-MM-DD")
			return
		}
		// Set to end of day
		endDate = endDate.Add(24*time.Hour - time.Second)
		params.EndDate = &endDate
	}

	// Parse amount parameters if provided
	if minAmountStr := ctx.Query("min_amount"); minAmountStr != "" {
		minAmount, err := strconv.ParseFloat(minAmountStr, 64)
		if err != nil {
			response.BadRequest(ctx, "invalid min_amount")
			return
		}
		params.MinAmount = &minAmount
	}

	if maxAmountStr := ctx.Query("max_amount"); maxAmountStr != "" {
		maxAmount, err := strconv.ParseFloat(maxAmountStr, 64)
		if err != nil {
			response.BadRequest(ctx, "invalid max_amount")
			return
		}
		params.MaxAmount = &maxAmount
	}

	// Set default values if not provided
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.Limit <= 0 {
		params.Limit = 20
	}
	if params.SortBy == "" {
		params.SortBy = "date"
	}
	if params.SortOrder == "" {
		params.SortOrder = "desc"
	}

	transactions, total, err := c.transactionService.ListTransactions(params)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Convert to response objects
	transactionResponses := make([]model.TransactionResponse, len(transactions))
	for i, transaction := range transactions {
		transactionResponses[i] = transaction.ToResponseWithRelations()
	}

	response.SuccessWithPagination(ctx, transactionResponses, total, params.Page, params.Limit)
}
