package controller

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/imaojia/easycash/internal/middleware"
	"github.com/imaojia/easycash/internal/model"
	"github.com/imaojia/easycash/internal/pkg/response"
	"github.com/imaojia/easycash/internal/service"
)

// AccountController handles account related requests
type AccountController struct {
	accountService *service.AccountService
}

// NewAccountController creates a new account controller
func NewAccountController(accountService *service.AccountService) *AccountController {
	return &AccountController{accountService: accountService}
}

// CreateAccount handles account creation
// @Summary Create a new account
// @Description Create a new account for the current user
// @Tags accounts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.AccountRequest true "Account details"
// @Success 200 {object} response.Response{data=model.AccountResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/accounts [post]
func (c *AccountController) CreateAccount(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	var req model.AccountRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	account, err := c.accountService.CreateAccount(userID, req)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, account)
}

// GetAccount retrieves an account by ID
// @Summary Get account by ID
// @Description Get an account by its ID
// @Tags accounts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Account ID"
// @Success 200 {object} response.Response{data=model.AccountResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/accounts/{id} [get]
func (c *AccountController) GetAccount(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	accountID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid account ID")
		return
	}

	account, err := c.accountService.GetAccountByID(userID, uint(accountID))
	if err != nil {
		response.NotFound(ctx, "account not found")
		return
	}

	response.Success(ctx, account.ToResponse())
}

// UpdateAccount updates an account
// @Summary Update an account
// @Description Update an account by its ID
// @Tags accounts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Account ID"
// @Param request body model.AccountRequest true "Account details"
// @Success 200 {object} response.Response{data=model.AccountResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/accounts/{id} [put]
func (c *AccountController) UpdateAccount(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	accountID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid account ID")
		return
	}

	var req model.AccountRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	account, err := c.accountService.UpdateAccount(userID, uint(accountID), req)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, account)
}

// DeleteAccount deletes an account
// @Summary Delete an account
// @Description Delete an account by its ID
// @Tags accounts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Account ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/accounts/{id} [delete]
func (c *AccountController) DeleteAccount(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	accountID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid account ID")
		return
	}

	if err := c.accountService.DeleteAccount(userID, uint(accountID)); err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, gin.H{"message": "account deleted successfully"})
}

// ListAccounts retrieves accounts for the current user
// @Summary List accounts
// @Description List accounts for the current user
// @Tags accounts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param type query string false "Account type"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Page size" default(10)
// @Success 200 {object} response.Response{data=[]model.AccountResponse}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/accounts [get]
func (c *AccountController) ListAccounts(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	var params model.AccountListParams
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}
	params.UserID = userID

	// Set default values if not provided
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.Limit <= 0 {
		params.Limit = 10
	}

	accounts, total, err := c.accountService.ListAccounts(params)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Convert to response objects
	accountResponses := make([]model.AccountResponse, len(accounts))
	for i, account := range accounts {
		accountResponses[i] = account.ToResponse()
	}

	response.SuccessWithPagination(ctx, accountResponses, total, params.Page, params.Limit)
}

// GetDefaultAccount retrieves the default account for the current user
// @Summary Get default account
// @Description Get the default account for the current user
// @Tags accounts
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=model.AccountResponse}
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/accounts/default [get]
func (c *AccountController) GetDefaultAccount(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	account, err := c.accountService.GetDefaultAccount(userID)
	if err != nil {
		response.NotFound(ctx, "default account not found")
		return
	}

	response.Success(ctx, account.ToResponse())
}
