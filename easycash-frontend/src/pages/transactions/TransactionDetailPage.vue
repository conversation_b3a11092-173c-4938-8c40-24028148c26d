<template>
  <q-page padding>
    <page-header
      title="Transaction Details"
      show-back-button
    >
      <template v-slot:actions>
        <q-btn
          color="primary"
          icon="edit"
          label="Edit"
          :to="`/transactions/${transactionId}/edit`"
          class="q-mr-sm"
        />
        <q-btn
          color="negative"
          icon="delete"
          label="Delete"
          @click="confirmDelete"
        />
      </template>
    </page-header>

    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading" message="Loading transaction data..." />

    <div v-if="transaction" class="row q-col-gutter-md">
      <!-- Transaction Summary -->
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section :class="headerClass">
            <div class="row items-center no-wrap">
              <q-avatar :color="transactionColor" text-color="white" size="3rem">
                <q-icon :name="transactionIcon" size="2rem" />
              </q-avatar>
              <div class="q-ml-md">
                <div class="text-h6">{{ transaction.description || 'No description' }}</div>
                <div class="text-subtitle2">{{ formatDate(transaction.date) }}</div>
              </div>
            </div>
          </q-card-section>

          <q-card-section>
            <div class="text-h5 q-mb-md" :class="amountClass">
              {{ amountPrefix }}{{ formatCurrency(transaction.amount) }}
            </div>

            <q-list>
              <q-item>
                <q-item-section>
                  <q-item-label caption>Type</q-item-label>
                  <q-item-label>{{ capitalizeFirst(transaction.type) }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section>
                  <q-item-label caption>Account</q-item-label>
                  <q-item-label>{{ transaction.account || 'Unknown account' }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section>
                  <q-item-label caption>Category</q-item-label>
                  <q-item-label>{{ transaction.category || 'Uncategorized' }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item v-if="transaction.location">
                <q-item-section>
                  <q-item-label caption>Location</q-item-label>
                  <q-item-label>{{ transaction.location }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item v-if="transaction.tags">
                <q-item-section>
                  <q-item-label caption>Tags</q-item-label>
                  <div>
                    <q-chip
                      v-for="tag in tags"
                      :key="tag"
                      size="sm"
                      color="primary"
                      text-color="white"
                      class="q-mr-xs"
                    >
                      {{ tag }}
                    </q-chip>
                  </div>
                </q-item-section>
              </q-item>

              <q-item v-if="transaction.notes">
                <q-item-section>
                  <q-item-label caption>Notes</q-item-label>
                  <q-item-label>{{ transaction.notes }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section>
                  <q-item-label caption>Created</q-item-label>
                  <q-item-label>{{ formatDateTime(transaction.createdAt) }}</q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section>
                  <q-item-label caption>Last Updated</q-item-label>
                  <q-item-label>{{ formatDateTime(transaction.updatedAt) }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </q-card>
      </div>

      <!-- Related Transactions -->
      <div class="col-12 col-md-8">
        <q-card>
          <q-card-section class="q-pb-none">
            <div class="text-h6">Related Transactions</div>
            <div class="text-caption">Transactions in the same category</div>
          </q-card-section>

          <q-card-section>
            <q-list v-if="relatedTransactions.length > 0" separator>
              <q-item v-for="relatedTx in relatedTransactions" :key="relatedTx.id" clickable @click="viewTransaction(relatedTx.id)">
                <q-item-section avatar>
                  <q-avatar :color="getTransactionColor(relatedTx.type)" text-color="white">
                    <q-icon :name="getTransactionIcon(relatedTx.type)" />
                  </q-avatar>
                </q-item-section>

                <q-item-section>
                  <q-item-label>{{ relatedTx.description || 'No description' }}</q-item-label>
                  <q-item-label caption>{{ formatDate(relatedTx.date) }}</q-item-label>
                </q-item-section>

                <q-item-section side>
                  <q-item-label :class="getAmountClass(relatedTx.type)">
                    {{ getAmountPrefix(relatedTx.type) }}{{ formatCurrency(relatedTx.amount) }}
                  </q-item-label>
                  <q-item-label caption>{{ relatedTx.account || 'Unknown account' }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>

            <div v-else class="text-center q-pa-md">
              <q-icon name="receipt_long" size="3rem" color="grey-5" />
              <div class="text-subtitle1 q-mt-sm">No related transactions found</div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <confirm-dialog
      v-model="showDeleteDialog"
      title="Delete Transaction"
      message="Are you sure you want to delete this transaction? This action cannot be undone."
      confirm-label="Delete"
      confirm-color="negative"
      :loading="deleteLoading"
      @confirm="deleteTransaction"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { date } from 'quasar';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import ConfirmDialog from 'src/components/common/ConfirmDialog.vue';
import { useTransactionStore } from 'src/stores/transaction.store';
import NotificationService from 'src/services/notification.service';

const props = defineProps({
  id: {
    type: Number,
    default: 0
  }
});

const router = useRouter();
const route = useRoute();
const transactionStore = useTransactionStore();

const loading = ref(false);
const error = ref<string | null>(null);
const showDeleteDialog = ref(false);
const deleteLoading = ref(false);

// Computed properties
const transactionId = computed(() => props.id || Number(route.params.id));
const transaction = computed(() => transactionStore.currentTransaction);

const tags = computed(() => {
  if (!transaction.value?.tags) return [];
  return transaction.value.tags.split(',').map(tag => tag.trim()).filter(Boolean);
});

const relatedTransactions = computed(() => {
  if (!transaction.value) return [];

  return transactionStore.getTransactionsByCategory(transaction.value.categoryId)
    .filter(tx => tx.id !== transaction.value?.id)
    .slice(0, 5);
});

// Transaction styling
const transactionIcon = computed(() => {
  if (!transaction.value) return 'receipt_long';

  switch (transaction.value.type) {
    case 'income':
      return 'arrow_upward';
    case 'expense':
      return 'arrow_downward';
    case 'transfer':
      return 'swap_horiz';
    default:
      return 'receipt_long';
  }
});

const transactionColor = computed(() => {
  if (!transaction.value) return 'grey';

  switch (transaction.value.type) {
    case 'income':
      return 'positive';
    case 'expense':
      return 'negative';
    case 'transfer':
      return 'info';
    default:
      return 'grey';
  }
});

const headerClass = computed(() => {
  if (!transaction.value) return '';

  switch (transaction.value.type) {
    case 'income':
      return 'bg-positive text-white';
    case 'expense':
      return 'bg-negative text-white';
    case 'transfer':
      return 'bg-info text-white';
    default:
      return 'bg-grey text-white';
  }
});

const amountClass = computed(() => {
  if (!transaction.value) return '';

  switch (transaction.value.type) {
    case 'income':
      return 'text-positive';
    case 'expense':
      return 'text-negative';
    default:
      return '';
  }
});

const amountPrefix = computed(() => {
  if (!transaction.value) return '';

  switch (transaction.value.type) {
    case 'income':
      return '+';
    case 'expense':
      return '-';
    default:
      return '';
  }
});

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Format date
const formatDate = (dateString: string) => {
  return date.formatDate(dateString, 'dddd, MMMM D, YYYY');
};

// Format date and time
const formatDateTime = (dateString: string) => {
  return date.formatDate(dateString, 'MMMM D, YYYY h:mm A');
};

// Capitalize first letter
const capitalizeFirst = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// Get transaction icon
const getTransactionIcon = (type: string) => {
  switch (type) {
    case 'income':
      return 'arrow_upward';
    case 'expense':
      return 'arrow_downward';
    case 'transfer':
      return 'swap_horiz';
    default:
      return 'receipt_long';
  }
};

// Get transaction color
const getTransactionColor = (type: string) => {
  switch (type) {
    case 'income':
      return 'positive';
    case 'expense':
      return 'negative';
    case 'transfer':
      return 'info';
    default:
      return 'grey';
  }
};

// Get amount class
const getAmountClass = (type: string) => {
  switch (type) {
    case 'income':
      return 'text-positive';
    case 'expense':
      return 'text-negative';
    default:
      return '';
  }
};

// Get amount prefix
const getAmountPrefix = (type: string) => {
  switch (type) {
    case 'income':
      return '+';
    case 'expense':
      return '-';
    default:
      return '';
  }
};

// View transaction
const viewTransaction = (id: number) => {
  router.push(`/transactions/${id}`);
};

// Confirm delete
const confirmDelete = () => {
  showDeleteDialog.value = true;
};

// Delete transaction
const deleteTransaction = async () => {
  deleteLoading.value = true;

  try {
    await transactionStore.delete(transactionId.value);
    NotificationService.success('Transaction deleted successfully');
    router.push('/transactions');
  } catch (err: any) {
    error.value = 'Failed to delete transaction. Please try again.';
    NotificationService.error('Failed to delete transaction');
    console.error('Delete transaction error:', err);
  } finally {
    deleteLoading.value = false;
    showDeleteDialog.value = false;
  }
};

// Load transaction data
const loadTransaction = async () => {
  loading.value = true;
  error.value = null;

  try {
    await transactionStore.get(transactionId.value);
  } catch (err: any) {
    error.value = 'Failed to load transaction. Please try again.';
    NotificationService.error('Failed to load transaction');
    console.error('Transaction detail error:', err);
  } finally {
    loading.value = false;
  }
};

// Load data on component mount
onMounted(() => {
  loadTransaction();
});
</script>
