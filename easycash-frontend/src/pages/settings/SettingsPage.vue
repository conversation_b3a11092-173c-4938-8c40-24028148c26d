<template>
  <q-page padding>
    <page-header title="Settings" />
    
    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading" message="Loading settings..." />
    
    <div class="row q-col-gutter-md">
      <!-- General Settings -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6">General Settings</div>
          </q-card-section>
          
          <q-card-section>
            <q-list>
              <q-item tag="label">
                <q-item-section>
                  <q-item-label>Currency</q-item-label>
                  <q-item-label caption>Default currency for new accounts</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-select
                    v-model="settings.defaultCurrency"
                    :options="currencies"
                    dense
                    options-dense
                    style="min-width: 150px"
                  />
                </q-item-section>
              </q-item>
              
              <q-item tag="label">
                <q-item-section>
                  <q-item-label>Date Format</q-item-label>
                  <q-item-label caption>Format for displaying dates</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-select
                    v-model="settings.dateFormat"
                    :options="dateFormats"
                    dense
                    options-dense
                    style="min-width: 150px"
                  />
                </q-item-section>
              </q-item>
              
              <q-item tag="label">
                <q-item-section>
                  <q-item-label>Theme</q-item-label>
                  <q-item-label caption>Application theme</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-select
                    v-model="settings.theme"
                    :options="themes"
                    dense
                    options-dense
                    style="min-width: 150px"
                  />
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
          
          <q-card-actions align="right">
            <q-btn color="primary" label="Save" @click="saveSettings" :loading="saving" />
          </q-card-actions>
        </q-card>
      </div>
      
      <!-- Notification Settings -->
      <div class="col-12 col-md-6">
        <q-card>
          <q-card-section>
            <div class="text-h6">Notification Settings</div>
          </q-card-section>
          
          <q-card-section>
            <q-list>
              <q-item tag="label">
                <q-item-section>
                  <q-item-label>Email Notifications</q-item-label>
                  <q-item-label caption>Receive notifications via email</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-toggle v-model="settings.emailNotifications" color="primary" />
                </q-item-section>
              </q-item>
              
              <q-item tag="label">
                <q-item-section>
                  <q-item-label>Budget Alerts</q-item-label>
                  <q-item-label caption>Get notified when you're close to budget limits</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-toggle v-model="settings.budgetAlerts" color="primary" />
                </q-item-section>
              </q-item>
              
              <q-item tag="label">
                <q-item-section>
                  <q-item-label>Monthly Reports</q-item-label>
                  <q-item-label caption>Receive monthly spending reports</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-toggle v-model="settings.monthlyReports" color="primary" />
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
          
          <q-card-actions align="right">
            <q-btn color="primary" label="Save" @click="saveNotificationSettings" :loading="saving" />
          </q-card-actions>
        </q-card>
      </div>
      
      <!-- Data Management -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">Data Management</div>
          </q-card-section>
          
          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-4">
                <q-card flat bordered>
                  <q-card-section>
                    <div class="text-subtitle1">Export Data</div>
                    <div class="text-caption">Download your financial data</div>
                  </q-card-section>
                  <q-card-actions>
                    <q-btn color="primary" label="Export" @click="exportData" />
                  </q-card-actions>
                </q-card>
              </div>
              
              <div class="col-12 col-md-4">
                <q-card flat bordered>
                  <q-card-section>
                    <div class="text-subtitle1">Import Data</div>
                    <div class="text-caption">Upload financial data</div>
                  </q-card-section>
                  <q-card-actions>
                    <q-btn color="primary" label="Import" @click="importData" />
                  </q-card-actions>
                </q-card>
              </div>
              
              <div class="col-12 col-md-4">
                <q-card flat bordered class="bg-red-1">
                  <q-card-section>
                    <div class="text-subtitle1 text-negative">Delete Account</div>
                    <div class="text-caption">Permanently delete your account and all data</div>
                  </q-card-section>
                  <q-card-actions>
                    <q-btn color="negative" label="Delete" @click="confirmDeleteAccount" />
                  </q-card-actions>
                </q-card>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    
    <!-- Delete Account Confirmation Dialog -->
    <confirm-dialog
      v-model="showDeleteDialog"
      title="Delete Account"
      message="Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost."
      confirm-label="Delete Account"
      confirm-color="negative"
      :loading="deleting"
      @confirm="deleteAccount"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import ConfirmDialog from 'src/components/common/ConfirmDialog.vue';
import { useAuthStore } from 'src/stores/auth.store';
import NotificationService from 'src/services/notification.service';

const router = useRouter();
const authStore = useAuthStore();

const loading = ref(false);
const saving = ref(false);
const deleting = ref(false);
const error = ref<string | null>(null);
const showDeleteDialog = ref(false);

// Settings
const settings = ref({
  defaultCurrency: 'USD',
  dateFormat: 'MM/DD/YYYY',
  theme: 'light',
  emailNotifications: true,
  budgetAlerts: true,
  monthlyReports: false
});

// Options
const currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CNY'];
const dateFormats = ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'];
const themes = ['light', 'dark', 'auto'];

// Save general settings
const saveSettings = async () => {
  saving.value = true;
  
  try {
    // In a real app, you would call an API to save settings
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    NotificationService.success('Settings saved successfully');
  } catch (err: any) {
    error.value = 'Failed to save settings. Please try again.';
    NotificationService.error('Failed to save settings');
  } finally {
    saving.value = false;
  }
};

// Save notification settings
const saveNotificationSettings = async () => {
  saving.value = true;
  
  try {
    // In a real app, you would call an API to save notification settings
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    NotificationService.success('Notification settings saved successfully');
  } catch (err: any) {
    error.value = 'Failed to save notification settings. Please try again.';
    NotificationService.error('Failed to save notification settings');
  } finally {
    saving.value = false;
  }
};

// Export data
const exportData = () => {
  NotificationService.info('Exporting data...');
  // In a real app, you would call an API to export data
};

// Import data
const importData = () => {
  NotificationService.info('Import functionality not implemented yet');
  // In a real app, you would implement file upload and import
};

// Confirm delete account
const confirmDeleteAccount = () => {
  showDeleteDialog.value = true;
};

// Delete account
const deleteAccount = async () => {
  deleting.value = true;
  
  try {
    // In a real app, you would call an API to delete the account
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    await authStore.logout();
    NotificationService.success('Account deleted successfully');
    router.push('/auth/login');
  } catch (err: any) {
    error.value = 'Failed to delete account. Please try again.';
    NotificationService.error('Failed to delete account');
  } finally {
    deleting.value = false;
    showDeleteDialog.value = false;
  }
};
</script>
