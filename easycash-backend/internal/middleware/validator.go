package middleware

import (
	"errors"
	"fmt"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"github.com/imaojia/easycash/internal/pkg/response"
)

// ValidationError represents a validation error
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// ValidationErrors represents a collection of validation errors
type ValidationErrors []ValidationError

// Validator is a middleware that validates request bodies
func Validator() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
	}
}

// ValidateJSON validates a JSON request body
func ValidateJSON(c *gin.Context, obj interface{}) bool {
	if err := c.ShouldBindJSON(obj); err != nil {
		var ve validator.ValidationErrors
		if errors.As(err, &ve) {
			out := make(ValidationErrors, len(ve))
			for i, fe := range ve {
				out[i] = ValidationError{
					Field:   getFieldName(fe.Field(), obj),
					Message: getErrorMsg(fe),
				}
			}
			response.BadRequest(c, fmt.Sprintf("Validation failed: %v", out))
		} else {
			response.BadRequest(c, fmt.Sprintf("Invalid request: %s", err.Error()))
		}
		return false
	}
	return true
}

// ValidateQuery validates query parameters
func ValidateQuery(c *gin.Context, obj interface{}) bool {
	if err := c.ShouldBindQuery(obj); err != nil {
		var ve validator.ValidationErrors
		if errors.As(err, &ve) {
			out := make(ValidationErrors, len(ve))
			for i, fe := range ve {
				out[i] = ValidationError{
					Field:   getFieldName(fe.Field(), obj),
					Message: getErrorMsg(fe),
				}
			}
			response.BadRequest(c, fmt.Sprintf("Validation failed: %v", out))
		} else {
			response.BadRequest(c, fmt.Sprintf("Invalid query parameters: %s", err.Error()))
		}
		return false
	}
	return true
}

// getFieldName returns the JSON field name for a struct field
func getFieldName(field string, obj interface{}) string {
	t := reflect.TypeOf(obj)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	
	for i := 0; i < t.NumField(); i++ {
		f := t.Field(i)
		if f.Name == field {
			jsonTag := f.Tag.Get("json")
			if jsonTag == "" {
				return field
			}
			
			parts := strings.Split(jsonTag, ",")
			if parts[0] == "-" {
				return field
			}
			
			return parts[0]
		}
	}
	
	return field
}

// getErrorMsg returns a human-readable error message for a validation error
func getErrorMsg(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return "This field is required"
	case "email":
		return "Invalid email format"
	case "min":
		if fe.Type().Kind() == reflect.String {
			return fmt.Sprintf("Must be at least %s characters long", fe.Param())
		}
		return fmt.Sprintf("Must be at least %s", fe.Param())
	case "max":
		if fe.Type().Kind() == reflect.String {
			return fmt.Sprintf("Must be at most %s characters long", fe.Param())
		}
		return fmt.Sprintf("Must be at most %s", fe.Param())
	case "oneof":
		return fmt.Sprintf("Must be one of: %s", fe.Param())
	case "gt":
		return fmt.Sprintf("Must be greater than %s", fe.Param())
	case "gte":
		return fmt.Sprintf("Must be greater than or equal to %s", fe.Param())
	case "lt":
		return fmt.Sprintf("Must be less than %s", fe.Param())
	case "lte":
		return fmt.Sprintf("Must be less than or equal to %s", fe.Param())
	case "eqfield":
		return fmt.Sprintf("Must be equal to %s", fe.Param())
	case "nefield":
		return fmt.Sprintf("Must not be equal to %s", fe.Param())
	case "unique":
		return "Must be unique"
	default:
		return fmt.Sprintf("Failed validation on %s", fe.Tag())
	}
}
