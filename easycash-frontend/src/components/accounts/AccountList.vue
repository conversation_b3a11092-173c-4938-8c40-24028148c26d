<template>
  <div class="account-list">
    <q-list separator>
      <q-item
        v-for="account in accounts"
        :key="account.id"
        clickable
        @click="$emit('select', account.id)"
      >
        <q-item-section avatar>
          <q-avatar :color="account.color || 'primary'" text-color="white">
            <q-icon :name="account.icon || 'account_balance'" />
          </q-avatar>
        </q-item-section>

        <q-item-section>
          <q-item-label>
            {{ account.name }}
            <q-badge v-if="account.is_default" color="primary" label="Default" class="q-ml-sm" />
          </q-item-label>
          <q-item-label caption>{{ account.type }}</q-item-label>
        </q-item-section>

        <q-item-section side>
          <q-item-label :class="account.balance >= 0 ? 'text-positive' : 'text-negative'">
            {{ formatCurrency(account.balance, account.currency) }}
          </q-item-label>
        </q-item-section>

        <q-item-section side v-if="showActions">
          <div class="row">
            <q-btn
              flat
              round
              dense
              color="primary"
              icon="edit"
              @click.stop="$emit('edit', account.id)"
            />
            <q-btn
              flat
              round
              dense
              color="negative"
              icon="delete"
              @click.stop="$emit('delete', account.id)"
            />
          </div>
        </q-item-section>
      </q-item>
    </q-list>

    <div v-if="accounts.length === 0" class="text-center q-pa-md">
      <q-icon name="account_balance" size="3rem" color="grey-5" />
      <div class="text-subtitle1 q-mt-sm">No accounts found</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import type { Account } from 'src/services/account.service';

defineProps({
  accounts: {
    type: Array as () => Account[],
    required: true
  },
  showActions: {
    type: Boolean,
    default: true
  }
});

defineEmits(['select', 'edit', 'delete']);

// Format currency
const formatCurrency = (value: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(value);
};
</script>
