import ApiService from './api.service'

export interface DailyStatistics {
  date: string
  income: number
  expense: number
  balance: number
}

export interface MonthlyStatistics {
  year: number
  month: number
  income: number
  expense: number
  balance: number
}

export interface YearlyStatistics {
  year: number
  income: number
  expense: number
  balance: number
}

export interface CategoryStatistics {
  category_id: number
  category_name: string
  amount: number
  percentage: number
}

export interface AccountStatistics {
  account_id: number
  account_name: string
  balance: number
  percentage: number
}

export const StatisticsService = {
  // Get daily statistics
  getDailyStatistics: async (startDate: string, endDate: string): Promise<DailyStatistics[]> => {
    try {
      return await ApiService.get<DailyStatistics[]>('/statistics/daily', {
        params: { start_date: startDate, end_date: endDate }
      })
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Get monthly statistics
  getMonthlyStatistics: async (startDate: string, endDate: string): Promise<MonthlyStatistics[]> => {
    try {
      return await ApiService.get<MonthlyStatistics[]>('/statistics/monthly', {
        params: { start_date: startDate, end_date: endDate }
      })
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Get yearly statistics
  getYearlyStatistics: async (startYear: number, endYear: number): Promise<YearlyStatistics[]> => {
    try {
      return await ApiService.get<YearlyStatistics[]>('/statistics/yearly', {
        params: { start_year: startYear, end_year: endYear }
      })
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Get category statistics
  getCategoryStatistics: async (
    startDate: string,
    endDate: string,
    type?: string
  ): Promise<CategoryStatistics[]> => {
    try {
      return await ApiService.get<CategoryStatistics[]>('/statistics/category', {
        params: { start_date: startDate, end_date: endDate, type }
      })
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // Get account statistics
  getAccountStatistics: async (): Promise<AccountStatistics[]> => {
    try {
      return await ApiService.get<AccountStatistics[]>('/statistics/account')
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

export default StatisticsService
