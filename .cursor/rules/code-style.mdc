---
description: 
globs: 
alwaysApply: true
---
# EasyCash 代码风格指南

## 前端代码规范 (Vue3 + TypeScript)

### TypeScript 规范

1. 类型定义
   - 使用 TypeScript 的严格模式
   - 为所有数据结构定义接口
   - 明确定义函数参数和返回值类型
   ```typescript
   // 好的写法
   interface Transaction {
     id: number;
     amount: number;
     category: string;
     date: Date;
   }
   
   function getTransaction(id: number): Promise<Transaction> {
     // ...
   }
   
   // 避免的写法
   function getTransaction(id: any): any {
     // ...
   }
   ```

2. 类型推断
   - 当类型明显时使用类型推断
   - 复杂类型显式声明
   ```typescript
   // 好的写法
   const amount = 0; // 自动推断为 number
   const transaction: Transaction = {
     id: 1,
     amount: 100,
     category: 'Food',
     date: new Date()
   };
   ```

3. 空值处理
   - 使用可选链操作符 ?.
   - 使用空值合并操作符 ??
   - 明确处理 undefined 和 null
   ```typescript
   const categoryName = transaction?.category?.name ?? 'Uncategorized';
   ```

### Vue 组件规范

1. 组件结构
   ```vue
   <template>
     <div class="transaction-list">
       <!-- 模板内容 -->
     </div>
   </template>
   
   <script setup lang="ts">
   // 导入
   import { ref, computed } from 'vue';
   import { useQuasar } from 'quasar';
   import type { Transaction } from '@/types';
   
   // Props 定义
   const props = defineProps<{
     transactions: Transaction[];
     loading?: boolean;
   }>();
   
   // Emits 定义
   const emit = defineEmits<{
     (e: 'update', id: number, data: Partial<Transaction>): void;
     (e: 'delete', id: number): void;
   }>();
   
   // 响应式状态
   const $q = useQuasar();
   const selectedId = ref<number | null>(null);
   
   // 计算属性
   const sortedTransactions = computed(() => 
     [...props.transactions].sort((a, b) => b.date.getTime() - a.date.getTime())
   );
   
   // 方法
   const handleDelete = async (id: number) => {
     try {
       await deleteTransaction(id);
       emit('delete', id);
       $q.notify({
         type: 'positive',
         message: '删除成功'
       });
     } catch (error) {
       $q.notify({
         type: 'negative',
         message: '删除失败'
       });
     }
   };
   </script>
   
   <style lang="scss" scoped>
   .transaction-list {
     // ...
   }
   </style>
   ```

2. Composables 规范
   ```typescript
   // useTransaction.ts
   export function useTransaction() {
     const transactions = ref<Transaction[]>([]);
     const loading = ref(false);
     
     const fetchTransactions = async () => {
       loading.value = true;
       try {
         transactions.value = await api.getTransactions();
       } catch (error) {
         // 错误处理
       } finally {
         loading.value = false;
       }
     };
     
     return {
       transactions,
       loading,
       fetchTransactions
     };
   }
   ```

3. Store 规范 (Pinia)
   ```typescript
   // transaction.store.ts
   export const useTransactionStore = defineStore('transaction', () => {
     const transactions = ref<Transaction[]>([]);
     
     const getById = computed(() => 
       (id: number) => transactions.value.find(t => t.id === id)
     );
     
     async function add(transaction: Omit<Transaction, 'id'>) {
       // 添加交易记录
     }
     
     return {
       transactions,
       getById,
       add
     };
   });
   ```

## 后端代码规范 (Go)

### 代码组织

1. 包结构
   ```go
   // internal/api/transaction/handler.go
   package transaction

   import (
       "net/http"
       
       "github.com/gin-gonic/gin"
   )

   type Handler struct {
       service Service
   }

   func NewHandler(s Service) *Handler {
       return &Handler{service: s}
   }

   func (h *Handler) Create(c *gin.Context) {
       // 处理请求
   }
   ```

2. 接口定义
   ```go
   // internal/service/transaction.go
   type Service interface {
       Create(ctx context.Context, req CreateTransactionRequest) (*Transaction, error)
       Get(ctx context.Context, id int64) (*Transaction, error)
       List(ctx context.Context, filter TransactionFilter) ([]Transaction, error)
   }
   ```

### 命名规范

1. 包名
   - 使用小写
   - 简短有意义
   - 避免下划线
   ```go
   package transaction  // 好
   package transactionService  // 避免
   ```

2. 接口名
   - 通常使用 er 后缀
   - 单一职责
   ```go
   type Reader interface {
       Read(p []byte) (n int, err error)
   }
   ```

3. 变量命名
   - 简短但有意义
   - 缩写保持一致性
   ```go
   var (
       maxCount int
       errNotFound = errors.New("not found")
   )
   ```

### 错误处理

1. 错误返回
   ```go
   func (s *service) Create(ctx context.Context, req Request) (*Response, error) {
       if err := req.Validate(); err != nil {
           return nil, fmt.Errorf("invalid request: %w", err)
       }
       
       // ...
   }
   ```

2. 错误包装
   ```go
   if err != nil {
       return fmt.Errorf("failed to create transaction: %w", err)
   }
   ```

### 并发处理

1. goroutine 使用
   ```go
   func (s *service) ProcessTransactions(ctx context.Context, ids []int64) error {
       var wg sync.WaitGroup
       errCh := make(chan error, len(ids))
       
       for _, id := range ids {
           wg.Add(1)
           go func(id int64) {
               defer wg.Done()
               if err := s.process(ctx, id); err != nil {
                   errCh <- err
               }
           }(id)
       }
       
       wg.Wait()
       close(errCh)
       
       return nil
   }
   ```

## 测试规范

### 前端测试 (Vitest)

```typescript
import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import TransactionList from './TransactionList.vue';

describe('TransactionList', () => {
  it('renders transactions correctly', () => {
    const transactions = [
      { id: 1, amount: 100, category: 'Food' }
    ];
    
    const wrapper = mount(TransactionList, {
      props: { transactions }
    });
    
    expect(wrapper.find('.transaction').exists()).toBe(true);
  });
});
```

### 后端测试 (Go)

```go
func TestCreateTransaction(t *testing.T) {
    tests := []struct {
        name    string
        req     CreateRequest
        wantErr bool
    }{
        {
            name: "valid request",
            req: CreateRequest{
                Amount: 100,
                Category: "Food",
            },
            wantErr: false,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            s := NewService()
            _, err := s.Create(context.Background(), tt.req)
            if (err != nil) != tt.wantErr {
                t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
``` 