import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  // Authentication routes
  {
    path: '/auth',
    component: () => import('layouts/AuthLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import('src/pages/auth/LoginPage.vue')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import('src/pages/auth/RegisterPage.vue')
      },
      {
        path: 'forgot-password',
        name: 'forgot-password',
        component: () => import('src/pages/auth/ForgotPasswordPage.vue')
      }
    ],
    meta: { requiresAuth: false }
  },

  // Main app routes
  {
    path: '/',
    component: () => import('layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'dashboard',
        component: () => import('src/pages/DashboardPage.vue')
      },

      // Transaction routes
      {
        path: 'transactions',
        name: 'transactions',
        component: () => import('src/pages/transactions/TransactionsPage.vue')
      },
      {
        path: 'transactions/new',
        name: 'transaction-new',
        component: () => import('src/pages/transactions/TransactionFormPage.vue'),
        meta: { mode: 'create' }
      },
      {
        path: 'transactions/:id',
        name: 'transaction-detail',
        component: () => import('src/pages/transactions/TransactionDetailPage.vue'),
        props: true
      },
      {
        path: 'transactions/:id/edit',
        name: 'transaction-edit',
        component: () => import('src/pages/transactions/TransactionFormPage.vue'),
        props: route => ({ id: Number(route.params.id), mode: 'edit' })
      },

      // Account routes
      {
        path: 'accounts',
        name: 'accounts',
        component: () => import('src/pages/accounts/AccountsPage.vue')
      },
      {
        path: 'accounts/new',
        name: 'account-new',
        component: () => import('src/pages/accounts/AccountFormPage.vue'),
        meta: { mode: 'create' }
      },
      {
        path: 'accounts/:id',
        name: 'account-detail',
        component: () => import('src/pages/accounts/AccountDetailPage.vue'),
        props: true
      },
      {
        path: 'accounts/:id/edit',
        name: 'account-edit',
        component: () => import('src/pages/accounts/AccountFormPage.vue'),
        props: route => ({ id: Number(route.params.id), mode: 'edit' })
      },

      // Category routes
      {
        path: 'categories',
        name: 'categories',
        component: () => import('src/pages/categories/CategoriesPage.vue')
      },
      {
        path: 'categories/new',
        name: 'category-new',
        component: () => import('src/pages/categories/CategoryFormPage.vue'),
        meta: { mode: 'create' }
      },
      {
        path: 'categories/:id/edit',
        name: 'category-edit',
        component: () => import('src/pages/categories/CategoryFormPage.vue'),
        props: route => ({ id: Number(route.params.id), mode: 'edit' })
      },

      // Budget routes
      {
        path: 'budgets',
        name: 'budgets',
        component: () => import('src/pages/budgets/BudgetsPage.vue')
      },
      {
        path: 'budgets/new',
        name: 'budget-new',
        component: () => import('src/pages/budgets/BudgetFormPage.vue'),
        meta: { mode: 'create' }
      },
      {
        path: 'budgets/:id/edit',
        name: 'budget-edit',
        component: () => import('src/pages/budgets/BudgetFormPage.vue'),
        props: route => ({ id: Number(route.params.id), mode: 'edit' })
      },

      // Statistics routes
      {
        path: 'statistics',
        name: 'statistics',
        component: () => import('src/pages/statistics/StatisticsPage.vue')
      },

      // Settings routes
      {
        path: 'settings',
        name: 'settings',
        component: () => import('src/pages/settings/SettingsPage.vue')
      },
      {
        path: 'profile',
        name: 'profile',
        component: () => import('src/pages/settings/ProfilePage.vue')
      }
    ]
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('src/pages/ErrorNotFound.vue'),
  },
];

export default routes;
