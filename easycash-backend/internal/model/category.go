package model

import (
	"time"

	"gorm.io/gorm"
)

// Category represents a transaction category
type Category struct {
	ID        uint      `json:"id" gorm:"primarykey"`
	UserID    uint      `json:"user_id" gorm:"not null;index"`
	Name      string    `json:"name" gorm:"size:50;not null"`
	Type      string    `json:"type" gorm:"size:20;not null"` // income, expense
	Icon      string    `json:"icon,omitempty" gorm:"size:50"`
	Color     string    `json:"color,omitempty" gorm:"size:20"`
	ParentID  *uint     `json:"parent_id,omitempty" gorm:"index"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	// Relations
	User       User       `json:"-" gorm:"foreignKey:UserID"`
	Parent     *Category  `json:"-" gorm:"foreignKey:ParentID"`
	Children   []Category `json:"-" gorm:"foreignKey:ParentID"`
	Transactions []Transaction `json:"-" gorm:"foreignKey:CategoryID"`
	Budgets    []Budget   `json:"-" gorm:"foreignKey:CategoryID"`
}

// CategoryRequest is used for creating or updating a category
type CategoryRequest struct {
	Name     string `json:"name" binding:"required,min=1,max=50"`
	Type     string `json:"type" binding:"required"`
	Icon     string `json:"icon,omitempty"`
	Color    string `json:"color,omitempty"`
	ParentID *uint  `json:"parent_id,omitempty"`
}

// CategoryResponse is used for returning category data
type CategoryResponse struct {
	ID        uint      `json:"id"`
	UserID    uint      `json:"user_id"`
	Name      string    `json:"name"`
	Type      string    `json:"type"`
	Icon      string    `json:"icon,omitempty"`
	Color     string    `json:"color,omitempty"`
	ParentID  *uint     `json:"parent_id,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Children  []CategoryResponse `json:"children,omitempty"`
}

// CategoryListParams is used for filtering categories
type CategoryListParams struct {
	UserID   uint   `form:"user_id"`
	Type     string `form:"type"`
	ParentID *uint  `form:"parent_id"`
	Page     int    `form:"page,default=1"`
	Limit    int    `form:"limit,default=50"`
}

// ToResponse converts a Category to CategoryResponse
func (c *Category) ToResponse() CategoryResponse {
	return CategoryResponse{
		ID:        c.ID,
		UserID:    c.UserID,
		Name:      c.Name,
		Type:      c.Type,
		Icon:      c.Icon,
		Color:     c.Color,
		ParentID:  c.ParentID,
		CreatedAt: c.CreatedAt,
		UpdatedAt: c.UpdatedAt,
	}
}

// ToResponseWithChildren converts a Category to CategoryResponse with children
func (c *Category) ToResponseWithChildren() CategoryResponse {
	response := c.ToResponse()
	
	if len(c.Children) > 0 {
		childrenResponses := make([]CategoryResponse, len(c.Children))
		for i, child := range c.Children {
			childrenResponses[i] = child.ToResponse()
		}
		response.Children = childrenResponses
	}
	
	return response
}
