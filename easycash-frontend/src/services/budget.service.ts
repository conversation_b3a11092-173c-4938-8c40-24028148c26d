import ApiService from './api.service'
import { PaginatedResponse } from './account.service'
import { Category } from './category.service'

export interface Budget {
  id: number
  user_id: number
  category_id: number
  amount: number
  period: string
  start_date: string
  end_date: string
  notes?: string
  created_at: string
  updated_at: string
  spent_amount?: number
  percentage?: number
  category?: Category
  transactions?: any[] // Added for compatibility with components that expect this property
}

export interface BudgetRequest {
  category_id: number
  amount: number
  period: string
  start_date: string
  end_date: string
  notes?: string
}

export interface BudgetListParams {
  category_id?: number
  period?: string
  start_date?: string
  end_date?: string
  page?: number
  limit?: number
  status?: string
}

export const BudgetService = {
  // Get all budgets
  getBudgets: async (params?: BudgetListParams): Promise<PaginatedResponse<Budget>> => {
    try {
      return await ApiService.get<PaginatedResponse<Budget>>('/budgets', { params })
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // Get budget by ID
  getBudget: async (id: number): Promise<Budget> => {
    try {
      return await ApiService.get<Budget>(`/budgets/${id}`)
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // Get active budgets
  getActiveBudgets: async (): Promise<Budget[]> => {
    try {
      return await ApiService.get<Budget[]>('/budgets/active')
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // Create new budget
  createBudget: async (budget: BudgetRequest): Promise<Budget> => {
    try {
      return await ApiService.post<Budget>('/budgets', budget)
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // Update budget
  updateBudget: async (id: number, budget: BudgetRequest): Promise<Budget> => {
    try {
      return await ApiService.put<Budget>(`/budgets/${id}`, budget)
    } catch (error) {
      return Promise.reject(error)
    }
  },

  // Delete budget
  deleteBudget: async (id: number): Promise<void> => {
    try {
      await ApiService.delete<void>(`/budgets/${id}`)
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

export default BudgetService
