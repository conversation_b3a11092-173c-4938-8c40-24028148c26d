import { defineRouter } from '#q-app/wrappers';
import {
  createM<PERSON>oryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from 'vue-router';
import routes from './routes';
import { useAuthStore } from 'src/stores/auth.store';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

export default defineRouter(function (/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : (process.env.VUE_ROUTER_MODE === 'history' ? createWebHistory : createWebHashHistory);

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in quasar.conf.js instead!
    // quasar.conf.js -> build -> vueRouterMode
    // quasar.conf.js -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE),
  });

  // Navigation guard to check auth status
  Router.beforeEach((to, from, next) => {
    // Check if the route requires authentication
    const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false);

    // Get auth status from store
    const authStore = useAuthStore();
    const isAuthenticated = authStore.isAuthenticated;

    // Logic for auth-required routes
    if (requiresAuth && !isAuthenticated) {
      // Redirect to login if trying to access auth-required route while not logged in
      next({ path: '/auth/login', query: { redirect: to.fullPath } });
    } else if (!requiresAuth && isAuthenticated && to.path.startsWith('/auth/')) {
      // Redirect to dashboard if trying to access auth pages while already logged in
      next({ path: '/' });
    } else {
      // Continue to route if authentication check passes
      next();
    }
  });

  // After navigation is complete, scroll to top
  Router.afterEach((to) => {
    // Check if the route has a hash (e.g., /page#section)
    if (!to.hash) {
      window.scrollTo(0, 0);
    }
  });

  return Router;
});
