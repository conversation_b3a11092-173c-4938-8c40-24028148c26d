<template>
  <q-page padding>
    <page-header title="Accounts">
      <template v-slot:actions>
        <q-btn
          color="primary"
          icon="add"
          label="New Account"
          to="/accounts/new"
        />
      </template>
    </page-header>
    
    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading" message="Loading accounts..." />
    
    <!-- Total Balance Card -->
    <div class="row q-mb-md">
      <div class="col-12">
        <q-card class="bg-primary text-white">
          <q-card-section>
            <div class="text-subtitle1">Total Balance</div>
            <div class="text-h4">{{ formatCurrency(totalBalance) }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    
    <!-- Account Type Tabs -->
    <div class="q-mb-md">
      <q-tabs
        v-model="activeTab"
        class="text-primary"
        active-color="primary"
        indicator-color="primary"
        align="justify"
        narrow-indicator
      >
        <q-tab name="all" label="All" />
        <q-tab name="cash" label="Cash" />
        <q-tab name="bank" label="Bank" />
        <q-tab name="credit" label="Credit" />
        <q-tab name="investment" label="Investment" />
        <q-tab name="other" label="Other" />
      </q-tabs>
    </div>
    
    <!-- Accounts List -->
    <div class="row q-col-gutter-md">
      <template v-if="filteredAccounts.length > 0">
        <div
          v-for="account in filteredAccounts"
          :key="account.id"
          class="col-12 col-md-6 col-lg-4"
        >
          <q-card class="account-card">
            <q-card-section class="bg-grey-2">
              <div class="row items-center no-wrap">
                <div class="col">
                  <div class="row items-center">
                    <q-avatar :color="account.color || 'primary'" text-color="white" class="q-mr-sm">
                      <q-icon :name="account.icon || 'account_balance'" />
                    </q-avatar>
                    <div>
                      <div class="text-subtitle1">{{ account.name }}</div>
                      <div class="text-caption">{{ account.type }}</div>
                    </div>
                  </div>
                </div>
                <div class="col-auto">
                  <q-badge v-if="account.is_default" color="primary" label="Default" />
                </div>
              </div>
            </q-card-section>
            
            <q-card-section>
              <div class="text-h5">{{ formatCurrency(account.balance, account.currency) }}</div>
            </q-card-section>
            
            <q-card-actions align="right">
              <q-btn flat color="grey" icon="receipt_long" label="Transactions" @click="viewTransactions(account.id)" />
              <q-btn flat color="primary" icon="edit" @click="editAccount(account.id)" />
              <q-btn flat color="negative" icon="delete" @click="confirmDelete(account)" />
            </q-card-actions>
          </q-card>
        </div>
      </template>
      
      <div v-else class="col-12">
        <q-card class="text-center q-pa-lg">
          <q-icon name="account_balance" size="4rem" color="grey-5" />
          <div class="text-h6 q-mt-md">No accounts found</div>
          <div class="q-mt-sm">
            <q-btn color="primary" label="Add Account" icon="add" to="/accounts/new" />
          </div>
        </q-card>
      </div>
    </div>
    
    <!-- Delete Confirmation Dialog -->
    <confirm-dialog
      v-model="showDeleteDialog"
      title="Delete Account"
      :message="`Are you sure you want to delete the account '${accountToDelete?.name}'?`"
      confirm-label="Delete"
      confirm-color="negative"
      :loading="deleteLoading"
      @confirm="deleteAccount"
    >
      <div class="text-body1 q-mb-md">
        This will permanently delete the account and all associated data. This action cannot be undone.
      </div>
    </confirm-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import ConfirmDialog from 'src/components/common/ConfirmDialog.vue';
import { useAccountStore } from 'src/stores/account.store';
import { Account } from 'src/services/account.service';
import NotificationService from 'src/services/notification.service';

const router = useRouter();
const accountStore = useAccountStore();

const loading = ref(false);
const error = ref<string | null>(null);
const activeTab = ref('all');
const showDeleteDialog = ref(false);
const deleteLoading = ref(false);
const accountToDelete = ref<Account | null>(null);

// Computed properties
const accounts = computed(() => accountStore.accounts);
const totalBalance = computed(() => accountStore.totalBalance);

const filteredAccounts = computed(() => {
  if (activeTab.value === 'all') {
    return accounts.value;
  }
  return accounts.value.filter(account => account.type.toLowerCase() === activeTab.value);
});

// Format currency
const formatCurrency = (value: number, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(value);
};

// View transactions for an account
const viewTransactions = (accountId: number) => {
  router.push({
    path: '/transactions',
    query: { account_id: accountId.toString() }
  });
};

// Edit account
const editAccount = (accountId: number) => {
  router.push(`/accounts/${accountId}/edit`);
};

// Confirm delete
const confirmDelete = (account: Account) => {
  accountToDelete.value = account;
  showDeleteDialog.value = true;
};

// Delete account
const deleteAccount = async () => {
  if (!accountToDelete.value) return;
  
  deleteLoading.value = true;
  
  try {
    await accountStore.deleteAccount(accountToDelete.value.id);
    NotificationService.success(`Account "${accountToDelete.value.name}" deleted successfully`);
    showDeleteDialog.value = false;
  } catch (err: any) {
    error.value = err.message || 'Failed to delete account';
    NotificationService.error('Failed to delete account');
  } finally {
    deleteLoading.value = false;
  }
};

// Load accounts
const loadAccounts = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    await accountStore.fetchAccounts();
  } catch (err: any) {
    error.value = 'Failed to load accounts. Please try again.';
    NotificationService.error('Failed to load accounts');
  } finally {
    loading.value = false;
  }
};

// Load data on component mount
onMounted(() => {
  loadAccounts();
});
</script>

<style scoped>
.account-card {
  height: 100%;
  transition: transform 0.2s;
}

.account-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
