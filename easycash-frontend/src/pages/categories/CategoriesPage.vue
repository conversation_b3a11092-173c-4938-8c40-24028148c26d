<template>
  <q-page padding>
    <page-header title="Categories">
      <template v-slot:actions>
        <q-btn
          color="primary"
          icon="add"
          label="New Category"
          to="/categories/new"
        />
      </template>
    </page-header>
    
    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading" message="Loading categories..." />
    
    <!-- Category Filters -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6">
            <q-btn-toggle
              v-model="filter"
              toggle-color="primary"
              :options="[
                { label: 'All', value: '' },
                { label: 'Expense', value: 'expense' },
                { label: 'Income', value: 'income' }
              ]"
              @update:model-value="fetchCategories"
            />
          </div>
          
          <div class="col-12 col-md-6">
            <q-input
              v-model="search"
              label="Search"
              dense
              outlined
              clearable
              @update:model-value="fetchCategories"
            >
              <template v-slot:append>
                <q-icon name="search" />
              </template>
            </q-input>
          </div>
        </div>
      </q-card-section>
    </q-card>
    
    <!-- Category List -->
    <q-card>
      <q-card-section>
        <category-list
          :categories="filteredCategories"
          @select="viewCategory"
          @edit="editCategory"
          @delete="confirmDeleteCategory"
        />
      </q-card-section>
    </q-card>
    
    <!-- Delete Confirmation Dialog -->
    <confirm-dialog
      v-model="showDeleteDialog"
      title="Delete Category"
      message="Are you sure you want to delete this category? This action cannot be undone."
      confirm-label="Delete"
      confirm-color="negative"
      :loading="deleteLoading"
      @confirm="deleteCategory"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import ConfirmDialog from 'src/components/common/ConfirmDialog.vue';
import CategoryList from 'src/components/categories/CategoryList.vue';
import { useCategoryStore } from 'src/stores/category.store';
import NotificationService from 'src/services/notification.service';

const router = useRouter();
const categoryStore = useCategoryStore();

const loading = ref(false);
const error = ref<string | null>(null);
const filter = ref('');
const search = ref('');
const showDeleteDialog = ref(false);
const deleteLoading = ref(false);
const categoryToDelete = ref<number | null>(null);

// Computed properties
const categories = computed(() => categoryStore.categories);

const filteredCategories = computed(() => {
  let result = categories.value;
  
  // Filter by type
  if (filter.value) {
    result = result.filter(category => category.type === filter.value);
  }
  
  // Filter by search term
  if (search.value) {
    const searchTerm = search.value.toLowerCase();
    result = result.filter(category => 
      category.name.toLowerCase().includes(searchTerm) || 
      (category.description && category.description.toLowerCase().includes(searchTerm))
    );
  }
  
  return result;
});

// Fetch categories
const fetchCategories = async () => {
  loading.value = true;
  error.value = null;
  
  try {
    await categoryStore.fetchCategories({
      type: filter.value || undefined,
      search: search.value || undefined
    });
  } catch (err: any) {
    error.value = 'Failed to load categories. Please try again.';
    NotificationService.error('Failed to load categories');
    console.error('Categories error:', err);
  } finally {
    loading.value = false;
  }
};

// View category
const viewCategory = (id: number) => {
  // For now, just edit the category since we don't have a detail page
  router.push(`/categories/${id}/edit`);
};

// Edit category
const editCategory = (id: number) => {
  router.push(`/categories/${id}/edit`);
};

// Confirm delete category
const confirmDeleteCategory = (id: number) => {
  categoryToDelete.value = id;
  showDeleteDialog.value = true;
};

// Delete category
const deleteCategory = async () => {
  if (!categoryToDelete.value) return;
  
  deleteLoading.value = true;
  
  try {
    await categoryStore.deleteCategory(categoryToDelete.value);
    NotificationService.success('Category deleted successfully');
    showDeleteDialog.value = false;
  } catch (err: any) {
    error.value = 'Failed to delete category. Please try again.';
    NotificationService.error('Failed to delete category');
    console.error('Delete category error:', err);
  } finally {
    deleteLoading.value = false;
  }
};

// Load categories on component mount
onMounted(() => {
  fetchCategories();
});
</script>
