<template>
  <q-page class="flex flex-center">
    <q-card class="login-card">
      <q-card-section class="text-center">
        <div class="text-h4 text-primary q-mb-md">EasyCash</div>
        <div class="text-h5 q-mb-lg">Sign In</div>
      </q-card-section>

      <q-card-section>
        <error-display :error="error" @dismiss="error = null" />

        <base-form
          ref="formRef"
          :loading="loading"
          submit-label="Sign In"
          @submit="onSubmit"
        >
          <q-input
            v-model="form.email"
            label="Email"
            type="email"
            :rules="[
              val => !!val || 'Email is required',
              val => /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(val) || 'Please enter a valid email'
            ]"
            lazy-rules
          >
            <template v-slot:prepend>
              <q-icon name="email" />
            </template>
          </q-input>

          <q-input
            v-model="form.password"
            label="Password"
            :type="showPassword ? 'text' : 'password'"
            :rules="[
              val => !!val || 'Password is required',
              val => val.length >= 6 || 'Password must be at least 6 characters'
            ]"
            lazy-rules
          >
            <template v-slot:prepend>
              <q-icon name="lock" />
            </template>
            <template v-slot:append>
              <q-icon
                :name="showPassword ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="showPassword = !showPassword"
              />
            </template>
          </q-input>
        </base-form>
      </q-card-section>

      <q-card-section class="text-center">
        <div class="q-mb-md">
          <router-link to="/auth/forgot-password" class="text-primary">
            Forgot Password?
          </router-link>
        </div>
        <div>
          Don't have an account?
          <router-link to="/auth/register" class="text-primary">
            Sign Up
          </router-link>
        </div>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from 'src/stores/auth.store';
import BaseForm from 'src/components/common/BaseForm.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import NotificationService from 'src/services/notification.service';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

const formRef = ref<InstanceType<typeof BaseForm> | null>(null);
const loading = ref(false);
const error = ref<string | null>(null);
const showPassword = ref(false);

const form = ref({
  email: '',
  password: ''
});

const onSubmit = async () => {
  try {
    loading.value = true;
    error.value = null;
    
    await authStore.login({
      email: form.value.email,
      password: form.value.password
    });
    
    NotificationService.success('Successfully logged in');
    
    // Redirect to the requested page or dashboard
    const redirectPath = route.query.redirect as string || '/';
    router.push(redirectPath);
  } catch (err: any) {
    error.value = err.message || 'Failed to login. Please check your credentials.';
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-card {
  width: 100%;
  max-width: 400px;
  padding: 20px;
}
</style>
