package controller

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/imaojia/easycash/internal/middleware"
	"github.com/imaojia/easycash/internal/model"
	"github.com/imaojia/easycash/internal/pkg/response"
	"github.com/imaojia/easycash/internal/service"
)

// CategoryController handles category related requests
type CategoryController struct {
	categoryService *service.CategoryService
}

// NewCategoryController creates a new category controller
func NewCategoryController(categoryService *service.CategoryService) *CategoryController {
	return &CategoryController{categoryService: categoryService}
}

// CreateCategory handles category creation
// @Summary Create a new category
// @Description Create a new category for the current user
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body model.CategoryRequest true "Category details"
// @Success 200 {object} response.Response{data=model.CategoryResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/categories [post]
func (c *CategoryController) CreateCategory(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	var req model.CategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	category, err := c.categoryService.CreateCategory(userID, req)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, category)
}

// GetCategory retrieves a category by ID
// @Summary Get category by ID
// @Description Get a category by its ID
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Category ID"
// @Success 200 {object} response.Response{data=model.CategoryResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/categories/{id} [get]
func (c *CategoryController) GetCategory(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	categoryID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid category ID")
		return
	}

	category, err := c.categoryService.GetCategoryByID(userID, uint(categoryID))
	if err != nil {
		response.NotFound(ctx, "category not found")
		return
	}

	response.Success(ctx, category.ToResponse())
}

// GetCategoryWithChildren retrieves a category with its children
// @Summary Get category with children
// @Description Get a category with its children by its ID
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Category ID"
// @Success 200 {object} response.Response{data=model.CategoryResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/categories/{id}/with-children [get]
func (c *CategoryController) GetCategoryWithChildren(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	categoryID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid category ID")
		return
	}

	category, err := c.categoryService.GetCategoryWithChildren(userID, uint(categoryID))
	if err != nil {
		response.NotFound(ctx, "category not found")
		return
	}

	response.Success(ctx, category.ToResponseWithChildren())
}

// UpdateCategory updates a category
// @Summary Update a category
// @Description Update a category by its ID
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Category ID"
// @Param request body model.CategoryRequest true "Category details"
// @Success 200 {object} response.Response{data=model.CategoryResponse}
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/categories/{id} [put]
func (c *CategoryController) UpdateCategory(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	categoryID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid category ID")
		return
	}

	var req model.CategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}

	category, err := c.categoryService.UpdateCategory(userID, uint(categoryID), req)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, category)
}

// DeleteCategory deletes a category
// @Summary Delete a category
// @Description Delete a category by its ID
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Category ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 401 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/categories/{id} [delete]
func (c *CategoryController) DeleteCategory(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	categoryID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(ctx, "invalid category ID")
		return
	}

	if err := c.categoryService.DeleteCategory(userID, uint(categoryID)); err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	response.Success(ctx, gin.H{"message": "category deleted successfully"})
}

// ListCategories retrieves categories for the current user
// @Summary List categories
// @Description List categories for the current user
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param type query string false "Category type"
// @Param parent_id query int false "Parent category ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Page size" default(50)
// @Success 200 {object} response.Response{data=[]model.CategoryResponse}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/categories [get]
func (c *CategoryController) ListCategories(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)

	var params model.CategoryListParams
	if err := ctx.ShouldBindQuery(&params); err != nil {
		response.BadRequest(ctx, err.Error())
		return
	}
	params.UserID = userID

	// Set default values if not provided
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.Limit <= 0 {
		params.Limit = 50
	}

	categories, total, err := c.categoryService.ListCategories(params)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Convert to response objects
	categoryResponses := make([]model.CategoryResponse, len(categories))
	for i, category := range categories {
		categoryResponses[i] = category.ToResponse()
	}

	response.SuccessWithPagination(ctx, categoryResponses, total, params.Page, params.Limit)
}

// GetCategoryHierarchy retrieves the full category hierarchy for the current user
// @Summary Get category hierarchy
// @Description Get the full category hierarchy for the current user
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param type query string false "Category type"
// @Success 200 {object} response.Response{data=[]model.CategoryResponse}
// @Failure 401 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/categories/hierarchy [get]
func (c *CategoryController) GetCategoryHierarchy(ctx *gin.Context) {
	userID := middleware.GetUserID(ctx)
	categoryType := ctx.Query("type")

	categories, err := c.categoryService.GetCategoryHierarchy(userID, categoryType)
	if err != nil {
		response.InternalError(ctx, err.Error())
		return
	}

	// Convert to response objects with children
	categoryResponses := make([]model.CategoryResponse, len(categories))
	for i, category := range categories {
		categoryResponses[i] = category.ToResponseWithChildren()
	}

	response.Success(ctx, categoryResponses)
}
