---
description: 
globs: 
alwaysApply: true
---
# EasyCash State Management Guide

## Pinia Store Guidelines
- Create separate stores by feature
- Use composition API syntax
- Define state types explicitly
- Implement actions for async operations

## Store Organization
- user.store.ts - User authentication and profile
- transaction.store.ts - Transaction management
- account.store.ts - Account management
- category.store.ts - Category management
- budget.store.ts - Budget tracking

## State Usage
- Use computed for derived state
- Avoid direct state mutation
- Implement proper error handling
- Cache appropriate data