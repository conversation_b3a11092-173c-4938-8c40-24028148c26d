import { boot } from 'quasar/wrappers';
import axios, { type AxiosResponse } from 'axios';
import type { AxiosInstance } from 'axios';
import { useAuthStore } from 'src/stores/auth.store';
import { handleAPIError } from 'src/utils/error';
import type { APIResponse } from 'src/types/api';

// Create a configured Axios instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.API_BASE_URL || 'http://localhost:8080',
  timeout: 10000, // 10 seconds
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
});

// Add interceptors for error handling
api.interceptors.request.use(
  config => {
    // Get token directly from localStorage instead of the store
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(new Error(error.message));
  }
);

api.interceptors.response.use(
  (response: AxiosResponse) => {
    const apiResponse = response.data as APIResponse;
    if (apiResponse.code !== 0) {
      return Promise.reject(new Error(apiResponse.message));
    }
    return response.data;
  },
  async error => {
    if (error.response?.status === 401) {
      // 处理 token 过期
      const authStore = useAuthStore();
      void authStore.logout();
      return Promise.reject(new Error('认证已过期，请重新登录'));
    }
    return handleAPIError(error);
  }
);

// 添加重试机制的请求方法
export async function retryRequest<T>(
  requestFn: () => Promise<T>,
  retries = 3,
  delay = 1000
): Promise<T> {
  try {
    return await requestFn();
  } catch (error) {
    if (retries > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
      return retryRequest(requestFn, retries - 1, delay * 2);
    }
    throw error;
  }
}

export default boot(({ app }) => {
  // for use inside Vue files (Options API) through this.$axios and this.$api
  app.config.globalProperties.$axios = axios;
  app.config.globalProperties.$api = api;
});

export { api };
