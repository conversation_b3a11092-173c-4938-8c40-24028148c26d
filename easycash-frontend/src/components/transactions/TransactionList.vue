<template>
  <div class="transaction-list">
    <div v-if="groupByDate" class="transaction-list-grouped">
      <template v-for="(group, date) in groupedTransactions" :key="date">
        <q-item class="transaction-date-header">
          <q-item-section>
            <q-item-label class="text-weight-bold">{{ formatDateHeader(date) }}</q-item-label>
          </q-item-section>
          <q-item-section side v-if="showDailySummary">
            <q-item-label>
              <span class="text-positive">+{{ formatCurrency(getDailyIncome(group)) }}</span>
              <span class="q-mx-xs">|</span>
              <span class="text-negative">-{{ formatCurrency(getDailyExpense(group)) }}</span>
            </q-item-label>
          </q-item-section>
        </q-item>
        
        <q-list separator>
          <transaction-list-item
            v-for="transaction in group"
            :key="transaction.id"
            :transaction="transaction"
            :show-actions="showActions"
            @click="$emit('select', transaction.id)"
            @edit="$emit('edit', transaction.id)"
            @delete="$emit('delete', transaction.id)"
          />
        </q-list>
      </template>
    </div>
    
    <q-list v-else separator>
      <transaction-list-item
        v-for="transaction in transactions"
        :key="transaction.id"
        :transaction="transaction"
        :show-actions="showActions"
        @click="$emit('select', transaction.id)"
        @edit="$emit('edit', transaction.id)"
        @delete="$emit('delete', transaction.id)"
      />
    </q-list>
    
    <div v-if="transactions.length === 0" class="text-center q-pa-md">
      <q-icon name="receipt_long" size="3rem" color="grey-5" />
      <div class="text-subtitle1 q-mt-sm">No transactions found</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';
import { date } from 'quasar';
import { Transaction } from 'src/services/transaction.service';
import TransactionListItem from './TransactionListItem.vue';

const props = defineProps({
  transactions: {
    type: Array as () => Transaction[],
    required: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  groupByDate: {
    type: Boolean,
    default: true
  },
  showDailySummary: {
    type: Boolean,
    default: true
  }
});

defineEmits(['select', 'edit', 'delete']);

// Group transactions by date
const groupedTransactions = computed(() => {
  const groups: Record<string, Transaction[]> = {};
  
  props.transactions.forEach(transaction => {
    // Extract date part only (YYYY-MM-DD)
    const dateKey = transaction.date.substring(0, 10);
    
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    
    groups[dateKey].push(transaction);
  });
  
  // Sort dates in descending order
  return Object.fromEntries(
    Object.entries(groups).sort((a, b) => {
      return new Date(b[0]).getTime() - new Date(a[0]).getTime();
    })
  );
});

// Format date header
const formatDateHeader = (dateString: string) => {
  const today = date.formatDate(new Date(), 'YYYY-MM-DD');
  const yesterday = date.formatDate(date.subtractFromDate(new Date(), { days: 1 }), 'YYYY-MM-DD');
  
  if (dateString === today) {
    return 'Today';
  } else if (dateString === yesterday) {
    return 'Yesterday';
  } else {
    return date.formatDate(dateString, 'dddd, MMMM D, YYYY');
  }
};

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Get daily income
const getDailyIncome = (transactions: Transaction[]) => {
  return transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);
};

// Get daily expense
const getDailyExpense = (transactions: Transaction[]) => {
  return transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);
};
</script>

<style scoped>
.transaction-date-header {
  background-color: rgba(0, 0, 0, 0.03);
  padding: 8px 16px;
}
</style>
