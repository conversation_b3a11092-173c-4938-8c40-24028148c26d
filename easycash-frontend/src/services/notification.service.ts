import { Notify, QNotifyCreateOptions } from 'quasar';

export type NotificationType = 'positive' | 'negative' | 'warning' | 'info';

export interface NotificationOptions extends Partial<QNotifyCreateOptions> {
  message: string;
  type?: NotificationType;
  timeout?: number;
}

export const NotificationService = {
  /**
   * Show a notification
   */
  show(options: NotificationOptions): void {
    const defaultOptions: QNotifyCreateOptions = {
      message: options.message,
      type: options.type || 'info',
      position: options.position || 'bottom-right',
      timeout: options.timeout || 3000,
      actions: options.actions || [{ icon: 'close', color: 'white' }]
    };

    Notify.create({
      ...defaultOptions,
      ...options
    });
  },

  /**
   * Show a success notification
   */
  success(message: string, options?: Partial<NotificationOptions>): void {
    this.show({
      message,
      type: 'positive',
      ...options
    });
  },

  /**
   * Show an error notification
   */
  error(message: string, options?: Partial<NotificationOptions>): void {
    this.show({
      message,
      type: 'negative',
      timeout: 5000,
      ...options
    });
  },

  /**
   * Show a warning notification
   */
  warning(message: string, options?: Partial<NotificationOptions>): void {
    this.show({
      message,
      type: 'warning',
      ...options
    });
  },

  /**
   * Show an info notification
   */
  info(message: string, options?: Partial<NotificationOptions>): void {
    this.show({
      message,
      type: 'info',
      ...options
    });
  }
};

export default NotificationService;
