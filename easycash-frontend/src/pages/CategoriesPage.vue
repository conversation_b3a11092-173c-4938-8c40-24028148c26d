<template>
  <q-page class="q-pa-md">
    <div class="row items-center q-mb-md">
      <div class="col">
        <div class="text-h5">Categories</div>
        <div class="text-subtitle2 text-grey-7">Manage your transaction categories</div>
      </div>
      <div class="col-auto">
        <q-btn 
          color="primary" 
          icon="add" 
          label="New Category" 
          @click="openCategoryDialog()"
        />
      </div>
    </div>

    <div class="row q-col-gutter-md">
      <!-- Category Types Selector -->
      <div class="col-12">
        <q-card class="q-mb-md">
          <q-card-section class="q-pa-sm">
            <q-tabs
              v-model="activeTab"
              dense
              class="text-grey"
              active-color="primary"
              indicator-color="primary"
              align="justify"
              narrow-indicator
            >
              <q-tab name="expense" label="Expense Categories" />
              <q-tab name="income" label="Income Categories" />
            </q-tabs>
          </q-card-section>
        </q-card>
      </div>

      <!-- Category Grid -->
      <div class="col-12">
        <div v-if="filteredCategories.length === 0" class="text-center q-pa-lg bg-grey-2 rounded-borders">
          <q-icon name="category" size="4rem" color="grey-5" />
          <div class="text-h6 q-mt-md">No Categories Yet</div>
          <div class="text-body2 text-grey-7 q-mb-md">Get started by adding your first category</div>
          <q-btn color="primary" label="Add Category" icon="add" @click="openCategoryDialog()" />
        </div>
        
        <div v-else class="row q-col-gutter-md">
          <div 
            v-for="category in filteredCategories" 
            :key="category.id" 
            class="col-12 col-sm-6 col-md-4 col-lg-3"
          >
            <q-card class="category-card">
              <q-card-section class="q-pb-none">
                <div class="row items-center no-wrap">
                  <div class="col-auto">
                    <q-avatar :color="category.color" text-color="white" size="42px">
                      <q-icon :name="category.icon || 'category'" />
                    </q-avatar>
                  </div>
                  <div class="col q-ml-sm">
                    <div class="text-subtitle1 ellipsis">{{ category.name }}</div>
                    <div class="text-caption text-grey">{{ category.type }}</div>
                  </div>
                  <div class="col-auto">
                    <q-btn flat round dense icon="more_vert">
                      <q-menu>
                        <q-list style="min-width: 100px">
                          <q-item clickable v-close-popup @click="openCategoryDialog(category)">
                            <q-item-section avatar>
                              <q-icon name="edit" color="primary" />
                            </q-item-section>
                            <q-item-section>Edit</q-item-section>
                          </q-item>
                          <q-item clickable v-close-popup @click="confirmDelete(category)">
                            <q-item-section avatar>
                              <q-icon name="delete" color="negative" />
                            </q-item-section>
                            <q-item-section>Delete</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </div>
                </div>
              </q-card-section>
              
              <q-card-section>
                <div v-if="category.description" class="text-body2 q-mb-md">
                  {{ category.description }}
                </div>
                
                <div class="text-caption text-grey-7">
                  <div v-if="category.budget" class="row items-center justify-between q-mb-xs">
                    <div>Monthly Budget:</div>
                    <div class="text-weight-medium">{{ formatCurrency(category.budget) }}</div>
                  </div>
                  <div v-if="category.transactions" class="row items-center justify-between">
                    <div>Transactions:</div>
                    <div class="text-weight-medium">{{ category.transactions }}</div>
                  </div>
                </div>
              </q-card-section>
              
              <q-separator v-if="category.budget && category.spent" />
              
              <q-card-section v-if="category.budget && category.spent" class="q-pt-sm">
                <div class="row items-center justify-between q-mb-xs">
                  <div class="text-caption">Budget used</div>
                  <div class="text-caption">
                    {{ formatCurrency(category.spent) }} / {{ formatCurrency(category.budget) }}
                  </div>
                </div>
                <q-linear-progress
                  :value="Math.min(category.spent / (category.budget || 1), 1)"
                  :color="getBudgetColor(category.spent, category.budget)"
                  size="8px"
                  rounded
                />
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>

    <!-- Category Form Dialog -->
    <q-dialog v-model="categoryDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section class="row items-center">
          <div class="text-h6">{{ editMode ? 'Edit Category' : 'New Category' }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-separator />

        <q-card-section>
          <q-form @submit="saveCategory" class="q-gutter-md">
            <q-input
              v-model="currentCategory.name"
              label="Category Name"
              outlined
              :rules="[val => !!val || 'Category name is required']"
            />
            
            <q-select
              v-model="currentCategory.type"
              :options="categoryTypes"
              label="Category Type"
              outlined
              emit-value
              map-options
              :rules="[val => !!val || 'Category type is required']"
            />
            
            <q-input
              v-model="currentCategory.description"
              label="Description"
              outlined
              type="textarea"
            />
            
            <div class="row q-col-gutter-md">
              <div class="col-12 col-sm-6">
                <q-select
                  v-model="currentCategory.icon"
                  :options="iconOptions"
                  label="Icon"
                  outlined
                >
                  <template v-slot:option="scope">
                    <q-item v-bind="scope.itemProps">
                      <q-item-section avatar>
                        <q-icon :name="scope.opt.value" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ scope.opt.label }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>
                  
                  <template v-slot:selected>
                    <q-icon :name="currentCategory.icon || 'category'" class="q-mr-xs" />
                    {{ currentCategory.icon ? getIconLabel(currentCategory.icon) : 'Select Icon' }}
                  </template>
                </q-select>
              </div>
              
              <div class="col-12 col-sm-6">
                <q-select
                  v-model="currentCategory.color"
                  :options="colorOptions"
                  label="Color"
                  outlined
                >
                  <template v-slot:option="scope">
                    <q-item v-bind="scope.itemProps">
                      <q-item-section avatar>
                        <q-badge :color="scope.opt.value" />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ scope.opt.label }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>
                  
                  <template v-slot:selected>
                    <q-badge :color="currentCategory.color || 'primary'" class="q-mr-xs" />
                    {{ currentCategory.color ? getColorLabel(currentCategory.color) : 'Select Color' }}
                  </template>
                </q-select>
              </div>
            </div>
            
            <q-input
              v-model.number="currentCategory.budget"
              label="Monthly Budget"
              outlined
              type="number"
              hint="Leave empty if not applicable"
            >
              <template v-slot:prepend>
                <q-icon name="attach_money" />
              </template>
            </q-input>
            
            <div class="row q-col-gutter-sm q-mt-md">
              <div class="col">
                <q-btn
                  color="primary"
                  label="Save"
                  type="submit"
                  class="full-width"
                />
              </div>
              <div class="col">
                <q-btn
                  color="grey-7"
                  label="Cancel"
                  v-close-popup
                  class="full-width"
                />
              </div>
            </div>
          </q-form>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="negative" text-color="white" />
          <span class="q-ml-sm">Delete Category</span>
        </q-card-section>

        <q-card-section>
          Are you sure you want to delete this category? This may affect existing transactions.
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Delete" color="negative" @click="deleteCategory" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';

// Define proper interfaces for our data structures
interface Category {
  id: number | null;
  name: string;
  type: string;
  description: string;
  icon: string;
  color: string;
  budget: number | null;
  spent: number | null;
  transactions: number;
}

const $q = useQuasar();

// State variables
const activeTab = ref('expense');
const loading = ref(false);
const categories = ref<Category[]>([]);
const categoryDialog = ref(false);
const deleteDialog = ref(false);
const editMode = ref(false);
const categoryToDelete = ref<Category | null>(null);
const currentCategory = ref<Category>({
  id: null,
  name: '',
  type: 'expense',
  description: '',
  icon: 'category',
  color: 'primary',
  budget: null,
  spent: null,
  transactions: 0
});

// Options for dropdowns
const categoryTypes = [
  { label: 'Expense', value: 'expense' },
  { label: 'Income', value: 'income' }
];

const iconOptions = [
  { label: 'Food', value: 'restaurant' },
  { label: 'Groceries', value: 'shopping_cart' },
  { label: 'Transportation', value: 'directions_car' },
  { label: 'Housing', value: 'home' },
  { label: 'Entertainment', value: 'movie' },
  { label: 'Shopping', value: 'shopping_bag' },
  { label: 'Health', value: 'health_and_safety' },
  { label: 'Education', value: 'school' },
  { label: 'Salary', value: 'payments' },
  { label: 'Investments', value: 'trending_up' },
  { label: 'Gifts', value: 'card_giftcard' },
  { label: 'Bills', value: 'receipt' },
  { label: 'Utilities', value: 'power' },
  { label: 'Travel', value: 'flight' },
  { label: 'Dining', value: 'fastfood' },
  { label: 'Sports', value: 'sports_soccer' },
  { label: 'Electronics', value: 'devices' },
  { label: 'Clothing', value: 'checkroom' },
  { label: 'Beauty', value: 'spa' },
  { label: 'Other', value: 'category' }
];

const colorOptions = [
  { label: 'Red', value: 'red' },
  { label: 'Pink', value: 'pink' },
  { label: 'Purple', value: 'purple' },
  { label: 'Deep Purple', value: 'deep-purple' },
  { label: 'Indigo', value: 'indigo' },
  { label: 'Blue', value: 'blue' },
  { label: 'Light Blue', value: 'light-blue' },
  { label: 'Cyan', value: 'cyan' },
  { label: 'Teal', value: 'teal' },
  { label: 'Green', value: 'green' },
  { label: 'Light Green', value: 'light-green' },
  { label: 'Lime', value: 'lime' },
  { label: 'Yellow', value: 'yellow' },
  { label: 'Amber', value: 'amber' },
  { label: 'Orange', value: 'orange' },
  { label: 'Deep Orange', value: 'deep-orange' },
  { label: 'Brown', value: 'brown' },
  { label: 'Grey', value: 'grey' },
  { label: 'Blue Grey', value: 'blue-grey' }
];

// Computed properties
const filteredCategories = computed(() => {
  return categories.value.filter(cat => cat.type === activeTab.value);
});

// Helper functions
const formatCurrency = (value: number | null) => {
  if (value === null || value === undefined) return '';
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(value);
};

const getIconLabel = (iconValue: string) => {
  const icon = iconOptions.find(option => option.value === iconValue);
  return icon ? icon.label : 'Custom';
};

const getColorLabel = (colorValue: string) => {
  const color = colorOptions.find(option => option.value === colorValue);
  return color ? color.label : 'Custom';
};

const getBudgetColor = (spent: number | null, budget: number | null) => {
  if (!spent || !budget) return 'grey';
  
  const ratio = spent / budget;
  if (ratio < 0.7) return 'positive';
  if (ratio < 0.9) return 'warning';
  return 'negative';
};

// CRUD operations
const fetchCategories = async () => {
  loading.value = true;
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock data
    categories.value = [
      // Expense categories
      { 
        id: 1, 
        name: 'Groceries', 
        type: 'expense', 
        description: 'Food and household supplies', 
        icon: 'shopping_cart', 
        color: 'green',
        budget: 400, 
        spent: 320.45,
        transactions: 12
      },
      { 
        id: 2, 
        name: 'Dining Out', 
        type: 'expense', 
        description: 'Restaurants and takeout', 
        icon: 'restaurant', 
        color: 'red',
        budget: 200, 
        spent: 180.75,
        transactions: 8
      },
      { 
        id: 3, 
        name: 'Transportation', 
        type: 'expense', 
        description: 'Gas, public transit, and ride shares', 
        icon: 'directions_car', 
        color: 'blue',
        budget: 300, 
        spent: 250.30,
        transactions: 15
      },
      { 
        id: 4, 
        name: 'Housing', 
        type: 'expense', 
        description: 'Rent and home maintenance', 
        icon: 'home', 
        color: 'brown',
        budget: 1500, 
        spent: 1500,
        transactions: 2
      },
      { 
        id: 5, 
        name: 'Entertainment', 
        type: 'expense', 
        description: 'Movies, concerts, and hobbies', 
        icon: 'movie', 
        color: 'purple',
        budget: 150, 
        spent: 87.50,
        transactions: 4
      },
      { 
        id: 6, 
        name: 'Shopping', 
        type: 'expense', 
        description: 'Clothing and non-essential items', 
        icon: 'shopping_bag', 
        color: 'pink',
        budget: 200, 
        spent: 150.25,
        transactions: 3
      },
      
      // Income categories
      { 
        id: 7, 
        name: 'Salary', 
        type: 'income', 
        description: 'Primary income source', 
        icon: 'payments', 
        color: 'green',
        budget: null, 
        spent: null,
        transactions: 2
      },
      { 
        id: 8, 
        name: 'Freelance', 
        type: 'income', 
        description: 'Side gigs and contract work', 
        icon: 'work', 
        color: 'blue',
        budget: null, 
        spent: null,
        transactions: 3
      },
      { 
        id: 9, 
        name: 'Investments', 
        type: 'income', 
        description: 'Dividends and capital gains', 
        icon: 'trending_up', 
        color: 'deep-purple',
        budget: null, 
        spent: null,
        transactions: 1
      }
    ];
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Failed to load categories:', err);
    $q.notify({
      color: 'negative',
      message: 'Failed to load categories',
      icon: 'error'
    });
  } finally {
    loading.value = false;
  }
};

const openCategoryDialog = (category: Category | null = null) => {
  if (category) {
    // Edit mode
    currentCategory.value = { ...category };
    editMode.value = true;
  } else {
    // Create mode
    currentCategory.value = {
      id: null,
      name: '',
      type: activeTab.value,
      description: '',
      icon: 'category',
      color: 'primary',
      budget: null,
      spent: null,
      transactions: 0
    };
    editMode.value = false;
  }
  
  categoryDialog.value = true;
};

const saveCategory = async () => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    if (editMode.value) {
      // Update existing category
      const index = categories.value.findIndex(c => c.id === currentCategory.value.id);
      if (index !== -1) {
        categories.value[index] = { ...currentCategory.value };
      }
      
      $q.notify({
        color: 'positive',
        message: 'Category updated successfully',
        icon: 'check_circle'
      });
    } else {
      // Create new category
      const newId = Math.max(...categories.value.map(c => c.id || 0), 0) + 1;
      const newCategory: Category = {
        ...currentCategory.value,
        id: newId
      };
      
      categories.value.push(newCategory);
      
      $q.notify({
        color: 'positive',
        message: 'Category created successfully',
        icon: 'check_circle'
      });
    }
    
    categoryDialog.value = false;
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error(editMode.value ? 'Failed to update category:' : 'Failed to create category:', err);
    $q.notify({
      color: 'negative',
      message: editMode.value ? 'Failed to update category' : 'Failed to create category',
      icon: 'error'
    });
  }
};

const confirmDelete = (category: Category) => {
  categoryToDelete.value = category;
  deleteDialog.value = true;
};

const deleteCategory = async () => {
  if (!categoryToDelete.value) return;
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Remove from local array
    const index = categories.value.findIndex(c => c.id === categoryToDelete.value?.id);
    if (index !== -1) {
      categories.value.splice(index, 1);
    }
    
    $q.notify({
      color: 'positive',
      message: 'Category deleted successfully',
      icon: 'check_circle'
    });
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Failed to delete category:', err);
    $q.notify({
      color: 'negative',
      message: 'Failed to delete category',
      icon: 'error'
    });
  } finally {
    categoryToDelete.value = null;
  }
};

// Initialize
onMounted(() => {
  void fetchCategories();
});
</script>

<style lang="scss" scoped>
.category-card {
  transition: all 0.3s ease;
  height: 100%;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
}
</style> 