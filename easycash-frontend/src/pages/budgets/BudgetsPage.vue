<template>
  <q-page padding>
    <page-header title="Budgets">
      <template v-slot:actions>
        <q-btn
          color="primary"
          icon="add"
          label="New Budget"
          to="/budgets/new"
        />
      </template>
    </page-header>

    <error-display :error="error" @dismiss="error = null" />
    <loading-overlay :loading="loading" message="Loading budgets..." />

    <!-- Budget Summary -->
    <div class="row q-col-gutter-md q-mb-lg">
      <div class="col-12 col-md-4">
        <q-card class="bg-primary text-white">
          <q-card-section>
            <div class="text-subtitle1">Total Budgets</div>
            <div class="text-h4">{{ totalBudgets }}</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-4">
        <q-card class="bg-positive text-white">
          <q-card-section>
            <div class="text-subtitle1">Total Budgeted</div>
            <div class="text-h4">{{ formatCurrency(totalBudgeted) }}</div>
          </q-card-section>
        </q-card>
      </div>

      <div class="col-12 col-md-4">
        <q-card class="bg-negative text-white">
          <q-card-section>
            <div class="text-subtitle1">Total Spent</div>
            <div class="text-h4">{{ formatCurrency(totalSpent) }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Budget Filters -->
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-4">
            <q-select
              v-model="filters.period"
              :options="periodOptions"
              label="Period"
              emit-value
              map-options
              clearable
              dense
              outlined
            />
          </div>

          <div class="col-12 col-md-4">
            <q-select
              v-model="filters.category_id"
              :options="categoryOptions"
              label="Category"
              emit-value
              map-options
              clearable
              dense
              outlined
            />
          </div>

          <div class="col-12 col-md-4">
            <q-select
              v-model="filters.status"
              :options="statusOptions"
              label="Status"
              emit-value
              map-options
              clearable
              dense
              outlined
            />
          </div>
        </div>

        <div class="row q-mt-md">
          <div class="col-12">
            <q-btn
              color="primary"
              label="Apply Filters"
              @click="fetchBudgets"
              :loading="loading"
              class="q-mr-sm"
            />
            <q-btn
              color="secondary"
              flat
              label="Reset Filters"
              @click="resetFilters"
              :disable="loading"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Budget List -->
    <q-card>
      <q-card-section>
        <budget-list
          :budgets="budgets"
          @select="viewBudget"
          @edit="editBudget"
          @delete="confirmDeleteBudget"
        />
      </q-card-section>
    </q-card>

    <!-- Delete Confirmation Dialog -->
    <confirm-dialog
      v-model="showDeleteDialog"
      title="Delete Budget"
      message="Are you sure you want to delete this budget? This action cannot be undone."
      confirm-label="Delete"
      confirm-color="negative"
      :loading="deleteLoading"
      @confirm="deleteBudget"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import PageHeader from 'src/components/common/PageHeader.vue';
import ErrorDisplay from 'src/components/common/ErrorDisplay.vue';
import LoadingOverlay from 'src/components/common/LoadingOverlay.vue';
import ConfirmDialog from 'src/components/common/ConfirmDialog.vue';
import BudgetList from 'src/components/budgets/BudgetList.vue';
import { useBudgetStore } from 'src/stores/budget.store';
import { useCategoryStore } from 'src/stores/category.store';
import { BudgetListParams } from 'src/services/budget.service';
import NotificationService from 'src/services/notification.service';

const router = useRouter();
const budgetStore = useBudgetStore();
const categoryStore = useCategoryStore();

const loading = ref(false);
const error = ref<string | null>(null);
const showDeleteDialog = ref(false);
const deleteLoading = ref(false);
const budgetToDelete = ref<number | null>(null);

// Filters
const filters = ref<BudgetListParams>({
  period: '',
  category_id: 0,
  status: ''
});

// Computed properties
const budgets = computed(() => budgetStore.budgets);
const totalBudgets = computed(() => budgetStore.budgets.length);
const totalBudgeted = computed(() => budgetStore.totalBudgetAmount);
const totalSpent = computed(() => budgetStore.totalSpentAmount);

// Filter options
const periodOptions = [
  { label: 'All Periods', value: '' },
  { label: 'Monthly', value: 'monthly' },
  { label: 'Quarterly', value: 'quarterly' },
  { label: 'Yearly', value: 'yearly' },
  { label: 'Custom', value: 'custom' }
];

const categoryOptions = computed(() => {
  return [
    { label: 'All Categories', value: 0 },
    ...categoryStore.categories.map(category => ({
      label: category.name,
      value: category.id
    }))
  ];
});

const statusOptions = [
  { label: 'All Statuses', value: '' },
  { label: 'Active', value: 'active' },
  { label: 'Upcoming', value: 'upcoming' },
  { label: 'Expired', value: 'expired' }
];

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Fetch budgets
const fetchBudgets = async () => {
  loading.value = true;
  error.value = null;

  try {
    // Remove empty filters
    const cleanFilters = { ...filters.value };
    Object.keys(cleanFilters).forEach(key => {
      const value = cleanFilters[key as keyof BudgetListParams];
      if (value === '' || value === 0) {
        delete cleanFilters[key as keyof BudgetListParams];
      }
    });

    await budgetStore.fetchBudgets(cleanFilters);
  } catch (err: any) {
    error.value = 'Failed to load budgets. Please try again.';
    NotificationService.error('Failed to load budgets');
    console.error('Budgets error:', err);
  } finally {
    loading.value = false;
  }
};

// Reset filters
const resetFilters = () => {
  filters.value = {
    period: '',
    category_id: 0,
    status: ''
  };

  fetchBudgets();
};

// View budget
const viewBudget = (id: number) => {
  // For now, just edit the budget since we don't have a detail page
  router.push(`/budgets/${id}/edit`);
};

// Edit budget
const editBudget = (id: number) => {
  router.push(`/budgets/${id}/edit`);
};

// Confirm delete budget
const confirmDeleteBudget = (id: number) => {
  budgetToDelete.value = id;
  showDeleteDialog.value = true;
};

// Delete budget
const deleteBudget = async () => {
  if (!budgetToDelete.value) return;

  deleteLoading.value = true;

  try {
    await budgetStore.deleteBudget(budgetToDelete.value);
    NotificationService.success('Budget deleted successfully');
    showDeleteDialog.value = false;
  } catch (err: any) {
    error.value = 'Failed to delete budget. Please try again.';
    NotificationService.error('Failed to delete budget');
    console.error('Delete budget error:', err);
  } finally {
    deleteLoading.value = false;
  }
};

// Load data on component mount
onMounted(async () => {
  // Load categories if not already loaded
  if (categoryStore.categories.length === 0) {
    await categoryStore.fetchCategories();
  }

  fetchBudgets();
});
</script>
