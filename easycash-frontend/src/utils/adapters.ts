import type { Transaction as ServiceTransaction } from 'src/services/transaction.service';
import type { Transaction as ModelTransaction } from 'src/types/models/transaction';

/**
 * Converts a transaction from the service format to the model format
 */
export function adaptServiceTransactionToModel(transaction: ServiceTransaction): ModelTransaction {
  return {
    id: transaction.id,
    date: transaction.date,
    description: transaction.description || '',
    category: transaction.category?.name || 'Uncategorized',
    amount: transaction.amount,
    type: transaction.type as 'income' | 'expense',
    createdAt: transaction.created_at,
    updatedAt: transaction.updated_at,
    // Store the IDs for filtering operations
    accountId: transaction.account_id,
    categoryId: transaction.category_id,
    // Store additional fields
    account: transaction.account?.name || 'Unknown account',
    notes: transaction.notes || '',
    location: transaction.location || '',
    tags: transaction.tags || '',
    imageUrl: transaction.image_url || ''
  };
}

/**
 * Converts an array of transactions from the service format to the model format
 */
export function adaptServiceTransactionsToModel(transactions: ServiceTransaction[]): ModelTransaction[] {
  return transactions.map(adaptServiceTransactionToModel);
}
