import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { APIResponse } from 'src/types/api/response'

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.API_URL || 'http://localhost:8080/api',
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor for API calls
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for API calls
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config
    
    // Handle 401 Unauthorized errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true
      
      // Try to refresh token or redirect to login
      localStorage.removeItem('token')
      window.location.href = '/auth/login'
      
      return Promise.reject(error)
    }
    
    return Promise.reject(error)
  }
)

// Generic API service
export const ApiService = {
  // GET request
  get: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    try {
      const response: AxiosResponse<APIResponse<T>> = await apiClient.get(url, config)
      return response.data.data as T
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // POST request
  post: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    try {
      const response: AxiosResponse<APIResponse<T>> = await apiClient.post(url, data, config)
      return response.data.data as T
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // PUT request
  put: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    try {
      const response: AxiosResponse<APIResponse<T>> = await apiClient.put(url, data, config)
      return response.data.data as T
    } catch (error) {
      return Promise.reject(error)
    }
  },
  
  // DELETE request
  delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    try {
      const response: AxiosResponse<APIResponse<T>> = await apiClient.delete(url, config)
      return response.data.data as T
    } catch (error) {
      return Promise.reject(error)
    }
  }
}

export default ApiService
