<template>
  <q-card-section class="q-px-lg">
    <q-form @submit="onSubmit" class="q-gutter-md">
      <q-input
        v-model="email"
        type="email"
        label="Email"
        outlined
        :rules="[val => !!val || 'Email is required', isValidEmail]"
      >
        <template v-slot:prepend>
          <q-icon name="email" />
        </template>
      </q-input>

      <q-input
        v-model="password"
        :type="isPwd ? 'password' : 'text'"
        label="Password"
        outlined
        :rules="[val => !!val || 'Password is required']"
      >
        <template v-slot:prepend>
          <q-icon name="lock" />
        </template>
        <template v-slot:append>
          <q-icon
            :name="isPwd ? 'visibility_off' : 'visibility'"
            class="cursor-pointer"
            @click="isPwd = !isPwd"
          />
        </template>
      </q-input>

      <div class="row items-center justify-between">
        <q-checkbox v-model="rememberMe" label="Remember me" />
        <q-btn flat dense color="primary" label="Forgot password?" />
      </div>

      <q-btn
        class="full-width q-py-sm"
        color="primary"
        label="Sign In"
        rounded
        type="submit"
        :loading="loading"
      />
    </q-form>
  </q-card-section>

  <q-card-section class="text-center">
    <p class="q-ma-none">Don't have an account?
      <router-link to="/register" class="text-primary">Sign Up</router-link>
    </p>
  </q-card-section>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';

const $q = useQuasar();
const router = useRouter();

const email = ref('');
const password = ref('');
const isPwd = ref(true);
const rememberMe = ref(false);
const loading = ref(false);

const isValidEmail = (val: string) => {
  const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailPattern.test(val) || 'Invalid email address';
};

const onSubmit = async () => {
  loading.value = true;
  
  try {
    // Here you would typically make an API call to authenticate
    // For now, we'll simulate successful login after a delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Redirect to dashboard after successful login
    await router.push('/');
    
    $q.notify({
      color: 'positive',
      message: 'Login successful',
      icon: 'check_circle'
    });
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Login failed:', err);
    $q.notify({
      color: 'negative',
      message: 'Login failed. Please check your credentials.',
      icon: 'error'
    });
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
a {
  text-decoration: none;
}
</style> 