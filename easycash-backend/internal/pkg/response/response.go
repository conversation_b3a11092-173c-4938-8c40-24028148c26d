package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response is the standard API response structure
type Response[T any] struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    T      `json:"data,omitempty"`
}

// PaginatedData represents paginated data
type PaginatedData[T any] struct {
	Items    []T   `json:"items"`
	Total    int64 `json:"total"`
	Page     int   `json:"page"`
	PageSize int   `json:"page_size"`
}

// Success sends a successful response with data
func Success[T any](c *gin.Context, data T) {
	c.JSON(http.StatusOK, Response[T]{
		Code:    0,
		Message: "success",
		Data:    data,
	})
}

// SuccessWithPagination sends a successful response with paginated data
func SuccessWithPagination[T any](c *gin.Context, items []T, total int64, page, pageSize int) {
	paginatedData := PaginatedData[T]{
		Items:    items,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}

	c.JSON(http.StatusOK, Response[PaginatedData[T]]{
		Code:    0,
		Message: "success",
		Data:    paginatedData,
	})
}

// Error sends an error response with a custom code
func Error(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, Response[any]{
		Code:    code,
		Message: message,
	})
}

// BadRequest sends a 400 Bad Request response
func BadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, Response[any]{
		Code:    400,
		Message: message,
	})
}

// Unauthorized sends a 401 Unauthorized response
func Unauthorized(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, Response[any]{
		Code:    401,
		Message: message,
	})
}

// Forbidden sends a 403 Forbidden response
func Forbidden(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, Response[any]{
		Code:    403,
		Message: message,
	})
}

// NotFound sends a 404 Not Found response
func NotFound(c *gin.Context, message string) {
	c.JSON(http.StatusNotFound, Response[any]{
		Code:    404,
		Message: message,
	})
}

// InternalError sends a 500 Internal Server Error response
func InternalError(c *gin.Context, message string) {
	c.JSON(http.StatusInternalServerError, Response[any]{
		Code:    500,
		Message: message,
	})
}
